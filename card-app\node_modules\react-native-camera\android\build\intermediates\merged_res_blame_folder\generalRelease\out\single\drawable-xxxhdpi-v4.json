[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}]