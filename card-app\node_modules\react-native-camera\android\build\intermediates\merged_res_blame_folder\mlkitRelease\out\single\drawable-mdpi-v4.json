[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\googleg_standard_color_18.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\googleg_standard_color_18.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_focused_holo.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_focused_holo.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\notification_bg_low_normal.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_low_normal.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\googleg_disabled_color_18.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\googleg_disabled_color_18.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl.png"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable-mdpi-v4\\notification_bg_normal.9.png", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_normal.9.png"}]