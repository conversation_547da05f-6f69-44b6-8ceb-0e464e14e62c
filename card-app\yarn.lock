# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ant-design/icons-react-native@^2.3.1", "@ant-design/icons-react-native@^2.3.2":
  version "2.3.2"
  resolved "https://registry.npmmirror.com/@ant-design/icons-react-native/download/@ant-design/icons-react-native-2.3.2.tgz"
  integrity sha1-huQS0c6NYpU0pdn0XNPY98ptrlY=

"@ant-design/react-native@^4.2.0":
  version "4.2.0"
  resolved "https://registry.npmmirror.com/@ant-design/react-native/download/@ant-design/react-native-4.2.0.tgz"
  integrity sha1-8Gwf4MqiFjmPfcge4EGWcNI34PU=
  dependencies:
    "@ant-design/icons-react-native" "^2.3.1"
    "@bang88/react-native-ultimate-listview" "^4.0.0"
    "@types/shallowequal" "^1.1.1"
    array-tree-filter "~2.1.0"
    babel-runtime "^6.x"
    deepmerge "^4.2.2"
    normalize-css-color "^1.0.2"
    react-native-collapsible "^1.6.0"
    react-native-modal-popover "^2.0.1"
    shallowequal "^1.1.0"
    tslint "^6.1.3"
    utility-types "^3.10.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.16.0", "@babel/code-frame@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz"
  integrity sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/code-frame@~7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.10.4.tgz?cache=0&sync_timestamp=1635561060995&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.10.4.tgz"
  integrity "sha1-Fo2ho26Q2miujUnA8bSMfGJJITo= sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg=="
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.20.0", "@babel/compat-data@^7.20.1":
  version "7.20.1"
  resolved "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.20.1.tgz"
  integrity sha512-EWZ4mE2diW3QALKvDMiXnbZpRvlj+nayZ112nK93SnhqOtpdsbVD4W+2tEoT3YNBAG9RBR0ISY758ZkOgsn6pQ==

"@babel/core@^7.1.0", "@babel/core@^7.1.6", "@babel/core@^7.12.3", "@babel/core@^7.14.0", "@babel/core@^7.16.0", "@babel/core@^7.7.2", "@babel/core@^7.7.5":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz"
  integrity sha1-xP9EBG9f4xBSXMnrTvUUfwxTdNQ=
  dependencies:
    "@babel/code-frame" "^7.16.0"
    "@babel/generator" "^7.16.0"
    "@babel/helper-compilation-targets" "^7.16.0"
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helpers" "^7.16.0"
    "@babel/parser" "^7.16.0"
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.1.2"
    semver "^6.3.0"
    source-map "^0.5.0"

"@babel/generator@^7.14.0", "@babel/generator@^7.16.0", "@babel/generator@^7.20.1", "@babel/generator@^7.7.2":
  version "7.20.4"
  resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.4.tgz"
  integrity sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA==
  dependencies:
    "@babel/types" "^7.20.2"
    "@jridgewell/gen-mapping" "^0.3.2"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.16.0", "@babel/helper-annotate-as-pure@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz"
  integrity sha512-duORpUiYrEpzKIop6iNbjnwKLAKnJ47csTyRACyEmWj0QdUrm5aqNJGHSSEQSUAvNW0ojX0dOmK9dZduvkfeXA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
  integrity sha512-yFQ0YCHoIqarl8BCRwBL8ulYUaZpz3bNsA7oFepAzee+8/+ImtADXNOmO5vJvsPff3qi+hvpkY/NYBTrBQgdNw==
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.0", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.0":
  version "7.20.0"
  resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz"
  integrity sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==
  dependencies:
    "@babel/compat-data" "^7.20.0"
    "@babel/helper-validator-option" "^7.18.6"
    browserslist "^4.21.3"
    semver "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.0", "@babel/helper-create-class-features-plugin@^7.18.6":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.2.tgz"
  integrity sha512-k22GoYRAHPYr9I+Gvy2ZQlAe5mGy8BqWst2wRt8cwIufWTxrsVshhIBvYNqC80N0GSFWTsqRVexOtfzlgOEDvA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6":
  version "7.19.0"
  resolved "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz"
  integrity sha512-htnV+mHX32DF81amCDrwIDr8nrp1PTm+3wfBN9/v8QJOLEioOCOG7qNyq0nHeFiWbT3Eb7gsPwEmV64UCQ1jzw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    regexpu-core "^5.1.0"

"@babel/helper-define-polyfill-provider@^0.2.4":
  version "0.2.4"
  resolved "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.2.4.tgz"
  integrity "sha1-iGeu150+psreQPgB77esXGaRaxA= sha512-OrpPZ97s+aPi6h2n1OXzdhVis1SGSsMU2aMHgLcOKfsp4/v1NWpx3CWT3lBj5eeBq9cDkPkh+YCfdF7O12uNDQ=="
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"
    semver "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz"
  integrity sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==

"@babel/helper-explode-assignable-expression@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz"
  integrity sha512-eyAYAsQmB80jNfg4baAtLeWAQHfHFiR483rzFK+BhETlGZaQC9bsfrugfXDCbRHLQbIA7U5NxhhOxN7p/dWIcg==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.19.0":
  version "7.19.0"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz"
  integrity sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==
  dependencies:
    "@babel/template" "^7.18.10"
    "@babel/types" "^7.19.0"

"@babel/helper-hoist-variables@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz"
  integrity sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.18.9":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz"
  integrity sha512-RxifAh2ZoVU67PyKIO4AMi1wTenGfMR/O/ae0CCRqwgBAt5v7xjdtRw7UoSbsreKrQn5t7r89eruK/9JjYHuDg==
  dependencies:
    "@babel/types" "^7.18.9"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.0", "@babel/helper-module-imports@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz"
  integrity sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-module-transforms@^7.16.0", "@babel/helper-module-transforms@^7.19.6":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz"
  integrity sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.20.1"
    "@babel/types" "^7.20.2"

"@babel/helper-optimise-call-expression@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz"
  integrity sha512-HP59oD9/fEHQkdcbgFCnbmgH5vIQTJbxh2yf+CdM89/glUNnuzr87Q8GIjGEnOktTROemO0Pe0iPAYbqZuOUiA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.8.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz"
  integrity sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==

"@babel/helper-remap-async-to-generator@^7.18.6":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz"
  integrity sha512-dI7q50YKd8BAv3VEfgg7PS7yD3Rtbi2J1XMXaalXO0W0164hYLnh8zpjRS0mte9MfVp/tltvr/cfdXPvJr1opA==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-wrap-function" "^7.18.9"
    "@babel/types" "^7.18.9"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.19.1":
  version "7.19.1"
  resolved "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz"
  integrity sha512-T7ahH7wV0Hfs46SFh5Jz3s0B6+o8g3c+7TMxu7xKfmHikg7EAZ3I2Qk9LFhjxXq8sL7UkP5JflezNwoZa8WvWw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.18.9"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/traverse" "^7.19.1"
    "@babel/types" "^7.19.0"

"@babel/helper-simple-access@^7.19.4", "@babel/helper-simple-access@^7.20.2":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz"
  integrity sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-skip-transparent-expression-wrappers@^7.18.9":
  version "7.20.0"
  resolved "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz"
  integrity sha512-5y1JYeNKfvnT8sZcK9DVRtpTbGiomYIHviSP3OQWmDPU3DeH4a1ZlT/N2lyQ5P8egjcRaT/Y9aNqUxK0WsnIIg==
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz"
  integrity sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  version "7.19.4"
  resolved "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz"
  integrity sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  version "7.19.1"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz"
  integrity sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==

"@babel/helper-validator-option@^7.14.5", "@babel/helper-validator-option@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz"
  integrity sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==

"@babel/helper-wrap-function@^7.18.9":
  version "7.19.0"
  resolved "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz"
  integrity sha512-txX8aN8CZyYGTwcLhlk87KRqncAzhh5TpQamZUa0/u3an36NtDpUP6bQgBCBcLeBs09R/OwQu3OjK0k/HwfNDg==
  dependencies:
    "@babel/helper-function-name" "^7.19.0"
    "@babel/template" "^7.18.10"
    "@babel/traverse" "^7.19.0"
    "@babel/types" "^7.19.0"

"@babel/helpers@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.16.0.tgz?cache=0&sync_timestamp=1635560664381&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.16.0.tgz"
  integrity sha1-h1UZyXnCMvQa371Do7A5jC44gYM=
  dependencies:
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz"
  integrity sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/parser@^7.1.0", "@babel/parser@^7.1.6", "@babel/parser@^7.14.0", "@babel/parser@^7.14.7", "@babel/parser@^7.16.0", "@babel/parser@^7.18.10", "@babel/parser@^7.20.1", "@babel/parser@^7.7.0", "@babel/parser@^7.7.2":
  version "7.20.3"
  resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz"
  integrity sha512-OP/s5a94frIPXwjzEcv5S/tpQfc6XhxYUnmWpgdqMWGgYCuErA3SzozaRAMQgSZWKeTJxht9aWAkUY+0UzvOFg==

"@babel/plugin-proposal-class-properties@^7.0.0", "@babel/plugin-proposal-class-properties@^7.1.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  integrity sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-proposal-export-default-from%2Fdownload%2F%40babel%2Fplugin-proposal-export-default-from-7.16.0.tgz"
  integrity sha1-+KBwCP/LDT3klF8+tSAi7MKLVq0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-default-from" "^7.16.0"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator@^7.1.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  integrity sha512-wQxQzxYeJqHcfppzBDnm1yAY0jSRkUXR2z8RePZYrKwMKgMlE8+Z6LUno+bd6LvbGh8Gltvy74+9pIYkr+XkKA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-object-rest-spread@^7.0.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.2.tgz"
  integrity sha512-Ks6uej9WFK+fvIMesSqbAto5dD8Dz4VuuFvGJFKgIGSkJuRGcrwGECPA1fDgQK3/DbExBJpEkTeYeB8geIFCSQ==
  dependencies:
    "@babel/compat-data" "^7.20.1"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.1"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  integrity sha512-Q40HEhs9DJQyaZfUjjn6vE8Cv4GmMHCYuMGIWUnlxH6400VGxOuwWsPt4FxXxJkC/5eOzgn0z21M9gMT4MOhbw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.0.0", "@babel/plugin-proposal-optional-chaining@^7.1.0":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz"
  integrity sha512-v5nwt4IqBXihxGsW2QmCWMDS3B3bzGIk/EQVZz2ei7f3NJl8NzAJVvUmpDW5q1CRNY+Beb/k58UAH1Km1N411w==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0", "@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.12.13"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-export-default-from%2Fdownload%2F%40babel%2Fplugin-syntax-export-default-from-7.16.0.tgz"
  integrity sha1-ZIUgZnd2eB+aDaF48kX/+FvJ428=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.16.0", "@babel/plugin-syntax-flow@^7.2.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.16.0.tgz?cache=0&sync_timestamp=1635740663618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-flow%2Fdownload%2F%40babel%2Fplugin-syntax-flow-7.16.0.tgz"
  integrity sha1-B0JwIdCT7XcBlAgiG+rwJyu8+uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-meta@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.16.0.tgz"
  integrity "sha1-+WJDlDFzZamojII1jT+EcRVGmPE= sha512-8zv2+xiPHwly31RK4RmnEYY5zziuF3O7W2kIDW+07ewWDh6Oi0dRq8kwvulRkFgt6DB97RlKs5c1y068iPlCUg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  version "7.14.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.16.0", "@babel/plugin-syntax-typescript@^7.7.2":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.16.0.tgz?cache=0&sync_timestamp=1635560844194&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-typescript%2Fdownload%2F%40babel%2Fplugin-syntax-typescript-7.16.0.tgz"
  integrity "sha1-L+6xPZM0zFguqREdNQb3cxdBebs= sha512-Xv6mEXqVdaqCBfJFyeab0fH2DnUoMsDmhamxsSi4j8nLd4Vtw213WMJr55xxqipC/YVWyPY3K0blJncPYji+dQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz"
  integrity sha512-9S9X9RUefzrsHZmKMbDXxweEH+YlE8JJEuat9FdvW9Qh1cw7W64jELCtWNkPBPX5En45uy28KGvA/AySqUh8CQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-async-to-generator@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz"
  integrity sha512-ARE5wZLKnTgPW7/1ftQmSi1CmkqqHo2DNmtztFhvgtOWSDfq0Cq9/9L+KnZNYSNrydBekhW3rwShduf59RoXag==
  dependencies:
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-remap-async-to-generator" "^7.18.6"

"@babel/plugin-transform-block-scoped-functions@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz"
  integrity sha512-ExUcOqpPWnliRcPqves5HJcJOvHvIIWfuS4sroBUenPuMdmW+SMHDakmtS7qOo13sVppmUijqeTv7qqGsvURpQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.0.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.2.tgz"
  integrity sha512-y5V15+04ry69OV2wULmwhEA6jwSWXO1TwAtIwiPXcvHcoOQUqpyMVd2bDsQJMW8AurjulIyUV8kDqtjSwHy1uQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.0.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.20.2.tgz"
  integrity sha512-9rbPp0lCVVoagvtEyQKSo5L8oo0nQS/iif+lwlAz29MccX2642vWDlSZK+2T2buxbopotId2ld7zZAzRfz9j1g==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.0"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.19.1"
    "@babel/helper-split-export-declaration" "^7.18.6"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.18.9.tgz"
  integrity sha512-+i0ZU1bCDymKakLxn5srGHrsAPRELC2WIbzwjLhHW9SIE1cPYkLCL0NlnXMZaM1vhfgA2+M7hySk42VBvrkBRw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-destructuring@^7.0.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.20.2.tgz"
  integrity sha512-mENM+ZHrvEgxLTBXUiQ621rRXZes3KWUv6NdQlrnr1TkWVw+hUjQBZuP2X32qKlrlG2BzgR95gkuCRSkJl8vIw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-exponentiation-operator@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz"
  integrity sha512-wzEtc0+2c88FVR34aQmiz56dxEkxr2g8DQb/KfaFa1JYXOFVsbhvAonFN6PwVWj++fKmku8NP80plJ5Et4wqHw==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-flow-strip-types@^7.0.0", "@babel/plugin-transform-flow-strip-types@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.16.0.tgz?cache=0&sync_timestamp=1635740663071&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-flow-strip-types%2Fdownload%2F%40babel%2Fplugin-transform-flow-strip-types-7.16.0.tgz"
  integrity sha1-7dlo3CBBwbaeRRomLpSNZlSnncI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-flow" "^7.16.0"

"@babel/plugin-transform-for-of@^7.0.0":
  version "7.18.8"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz"
  integrity sha512-yEfTRnjuskWYo0k1mHUqrVWaZwrdq8AYbfrpqULOJOaucGSp4mNMVps+YtA8byoevxS/urwU75vyhQIxcCgiBQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-function-name@^7.0.0":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz"
  integrity sha512-WvIBoRPaJQ5yVHzcnJFor7oS5Ls0PYixlTYE63lCj2RtdQEl15M68FXQlxnG6wdraJIXRdR7KI+hQ7q/9QjrCQ==
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.0.0":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz"
  integrity sha512-IFQDSRoTPnrAIrI5zoZv73IFeZu2dhu6irxQjY9rNjTT53VmKg9fenjvoiOWOkJ6mm4jKVPtdMzBY98Fp4Z4cg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz"
  integrity sha512-qSF1ihLGO3q+/g48k85tUjD033C29TNTVB2paCwZPVmOsjn9pClvYYrM2VeJpBY2bcNkuny0YUyTNRyRxJ54KA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.1.0":
  version "7.19.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.19.6.tgz"
  integrity sha512-8PIa1ym4XRTKuSsOUXqDG0YaOlEuTVvHMe5JCfgBMOtHvJKw/4NGovEGN33viISshG/rZNVrACiBmPQLvWN8xQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.19.6"
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-simple-access" "^7.19.4"

"@babel/plugin-transform-object-assign@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-object-assign/download/@babel/plugin-transform-object-assign-7.16.0.tgz"
  integrity sha1-dQxyY5fx9kAvsc7/6dj/NZXIoN8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-super@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz"
  integrity sha512-uvGz6zk+pZoS1aTZrOvrbj6Pp/kK2mp45t2B+bTDre2UgsZZ8EZLSJtUg7m/no0zOJUWgFONpB7Zv9W2tSaFlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.20.1":
  version "7.20.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.3.tgz"
  integrity sha512-oZg/Fpx0YDrj13KsLyO8I/CX3Zdw7z0O9qOd95SqcoIzuqy/WTGWvePeHAnZCN54SfdyjHcb1S30gc8zlzlHcA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-property-literals@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz"
  integrity sha512-cYcs6qlgafTud3PAzrrRNbQtfpQ8+y/+M5tKmksS9+M1ckbH6kzY8MrexEM9mcA6JDsukE19iIRvAyYl463sMg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.16.0.tgz"
  integrity sha1-mgrYqo6HkIg6e9Jzb2YimlgSVnY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.16.0.tgz"
  integrity sha1-CSAhWKu8cWoIMw85K/uY1rms+gw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.16.0.tgz"
  integrity sha1-1AyVnXgDquOCJFlFhXSGk+hMCiI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.16.0.tgz"
  integrity sha1-VbeX1JYMPeBOB60cBHbivGpIifE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-jsx" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/plugin-transform-regenerator@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz"
  integrity sha512-poqRI2+qiSdeldcz4wTSTXBRryoq3Gc70ye7m7UD5Ww0nE29IXqMl6r7Nd15WBgRd74vloEMlShtH6CKxVzfmQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    regenerator-transform "^0.15.0"

"@babel/plugin-transform-runtime@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-runtime%2Fdownload%2F%40babel%2Fplugin-transform-runtime-7.16.0.tgz"
  integrity sha1-P+DaNsLwg0vvfE0+fystsO4MiQk=
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    babel-plugin-polyfill-corejs2 "^0.2.3"
    babel-plugin-polyfill-corejs3 "^0.3.0"
    babel-plugin-polyfill-regenerator "^0.2.3"
    semver "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz"
  integrity sha512-eCLXXJqv8okzg86ywZJbRn19YJHU4XUa55oz2wbHhaQVn/MM+XhukiT7SYqp/7o00dg52Rj51Ny+Ecw4oyoygw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.0.0":
  version "7.19.0"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz"
  integrity sha512-RsuMk7j6n+r752EtzyScnWkQyuJdli6LdO5Klv8Yx0OfPVTcQkIUfS8clx5e9yHXzlnhOZF3CbQ8C2uP5j074w==
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.18.9"

"@babel/plugin-transform-sticky-regex@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz"
  integrity sha512-kfiDrDQ+PBsQDO85yj1icueWMfGfJFKN1KCkndygtu/C9+XUfydLC8Iv5UYJqRwy4zk8EcplRxEOeLyjq1gm6Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.0.0":
  version "7.18.9"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz"
  integrity sha512-S8cOWfT82gTezpYOiVaGHrCbhlHgKhQt8XH5ES46P2XWmX92yisoZywf5km75wv5sYcXDUCLMmMxOLCtthDgMA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typescript@^7.16.0", "@babel/plugin-transform-typescript@^7.5.0":
  version "7.16.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.16.1.tgz?cache=0&sync_timestamp=1635663706468&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-typescript%2Fdownload%2F%40babel%2Fplugin-transform-typescript-7.16.1.tgz"
  integrity sha1-zAZwsoIrAzg1W8Gz0iRqQrgWZAk=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-typescript" "^7.16.0"

"@babel/plugin-transform-unicode-regex@^7.0.0":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz"
  integrity sha512-gE7A6Lt7YLnNOL3Pb9BNeZvi+d8l7tcRrG4+pwJjK9hD2xX4mEvjlQW60G9EEmfXVYRPv9VRQcyegIVHCql/AA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-flow@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/preset-flow/download/@babel/preset-flow-7.16.0.tgz"
  integrity "sha1-nx9ucnFNeUYNSAWMtWWPyH2nFQs= sha512-e5NE1EoPMpoHFkyFkMSj2h9tu7OolARcUHki8mnBv4NiFK9so+UrhbvT9mV99tMJOUEx8BOj67T6dXvGcTeYeQ=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-flow-strip-types" "^7.16.0"

"@babel/preset-typescript@^7.1.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/preset-typescript/download/@babel/preset-typescript-7.16.0.tgz?cache=0&sync_timestamp=1635579061274&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fpreset-typescript%2Fdownload%2F%40babel%2Fpreset-typescript-7.16.0.tgz"
  integrity "sha1-sLTxBbhV+z1jHsA2zcnR/9H6Xqw= sha512-txegdrZYgO9DlPbv+9QOVpMnKbOtezsLHWsnsRF4AjbSIsVaujrq1qg8HK0mxQpWv0jnejt0yEoW1uWpvbrDTg=="
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-transform-typescript" "^7.16.0"

"@babel/register@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/register/download/@babel/register-7.16.0.tgz?cache=0&sync_timestamp=1635737101826&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fregister%2Fdownload%2F%40babel%2Fregister-7.16.0.tgz"
  integrity "sha1-9dKqFN83z3FGuXWffFOBg2DyTsY= sha512-lzl4yfs0zVXnooeLE0AAfYaT7F3SPA8yB2Bj4W1BiZwLbMS3MZH35ZvCWSRHvneUugwuM+Wsnrj7h0F7UmU3NQ=="
  dependencies:
    clone-deep "^4.0.1"
    find-cache-dir "^2.0.0"
    make-dir "^2.1.0"
    pirates "^4.0.0"
    source-map-support "^0.5.16"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.16.0", "@babel/runtime@^7.8.4":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.0.tgz?cache=0&sync_timestamp=1635554597219&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.16.0.tgz"
  integrity sha1-4nuXfy4giLokdIv5m14d7OZOTws=
  dependencies:
    regenerator-runtime "^0.13.4"

"@babel/template@^7.0.0", "@babel/template@^7.16.0", "@babel/template@^7.18.10", "@babel/template@^7.3.3":
  version "7.18.10"
  resolved "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz"
  integrity sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.18.10"
    "@babel/types" "^7.18.10"

"@babel/traverse@^7.1.0", "@babel/traverse@^7.13.0", "@babel/traverse@^7.14.0", "@babel/traverse@^7.16.0", "@babel/traverse@^7.19.0", "@babel/traverse@^7.19.1", "@babel/traverse@^7.20.1", "@babel/traverse@^7.7.0", "@babel/traverse@^7.7.2", "@babel/traverse@^7.7.4":
  version "7.20.1"
  resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz"
  integrity sha512-d3tN8fkVJwFLkHkBN479SOsw4DMZnz8cdbL/gvuDuzy3TS6Nfw80HuQqhw1pITbIruHyh7d1fMA47kWzmcUEGA==
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/generator" "^7.20.1"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.19.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.20.1"
    "@babel/types" "^7.20.0"
    debug "^4.1.0"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.16.0", "@babel/types@^7.18.10", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.19.0", "@babel/types@^7.20.0", "@babel/types@^7.20.2", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.7.0":
  version "7.20.2"
  resolved "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz"
  integrity sha512-FnnvsNWgZCr232sqtXggapvlkk/tuwR/qhGzcmxI0GXLCjmPYQPzio2FbdlWuY6y1sHFfQKk+rRbUZ9VStQMog==
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    to-fast-properties "^2.0.0"

"@bang88/react-native-ultimate-listview@^4.0.0":
  version "4.0.0"
  resolved "https://registry.npmmirror.com/@bang88/react-native-ultimate-listview/download/@bang88/react-native-ultimate-listview-4.0.0.tgz"
  integrity sha1-+MDa7h0JHqW4GJFQHX911HF8Q0c=

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://registry.npmmirror.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cnakazawa/watch@^1.0.3":
  version "1.0.4"
  resolved "https://registry.npmmirror.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz"
  integrity "sha1-+GSuhQBND8q29QvpFBxNo2jRZWo= sha512-v9kIhKwjeZThiWrLmj0y17CWoyddASLj9O2yvbZkbvw/N3rWOYy9zkV66ursAoVr0mV15bL8g0c4QZUE6cdDoQ=="
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@egjs/hammerjs@^2.0.17":
  version "2.0.17"
  resolved "https://registry.npmmirror.com/@egjs/hammerjs/download/@egjs/hammerjs-2.0.17.tgz"
  integrity sha1-XcAq91pqBuTC2wICyuOMkmOJUSQ=
  dependencies:
    "@types/hammerjs" "^2.0.36"

"@eslint/eslintrc@^1.0.4":
  version "1.0.4"
  resolved "https://registry.npmmirror.com/@eslint/eslintrc/download/@eslint/eslintrc-1.0.4.tgz"
  integrity sha1-3+D/e6JwhI0Qxa3QcV4ElkwDSzE=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.0.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@expo/config-plugins@^3.0.6":
  version "3.1.0"
  resolved "https://registry.npmmirror.com/@expo/config-plugins/download/@expo/config-plugins-3.1.0.tgz"
  integrity sha1-B1L/M8Xqshz0IDSkTnnfl/D4Z/g=
  dependencies:
    "@expo/config-types" "^42.0.0"
    "@expo/json-file" "8.2.33"
    "@expo/plist" "0.0.14"
    chalk "^4.1.2"
    debug "^4.3.1"
    find-up "~5.0.0"
    fs-extra "9.0.0"
    getenv "^1.0.0"
    glob "7.1.6"
    resolve-from "^5.0.0"
    semver "^7.3.5"
    slash "^3.0.0"
    xcode "^3.0.1"
    xml2js "^0.4.23"

"@expo/config-types@^42.0.0":
  version "42.0.0"
  resolved "https://registry.npmmirror.com/@expo/config-types/download/@expo/config-types-42.0.0.tgz"
  integrity "sha1-Pj4SXsCSwMNNv68ZvlSAQC3j1nc= sha512-Rj02OMZke2MrGa/1Y/EScmR7VuWbDEHPJyvfFyyLbadUt+Yv6isCdeFzDt71I7gJlPR9T4fzixeYLrtXXOTq0w=="

"@expo/json-file@8.2.33":
  version "8.2.33"
  resolved "https://registry.npmmirror.com/@expo/json-file/download/@expo/json-file-8.2.33.tgz"
  integrity "sha1-ePVvM6LPuAeyPIHgAjejMVmqHzI= sha512-CDnhjdirUs6OdN5hOSTJ2y3i9EiJMk7Z5iDljC5xyCHCrUex7oyI8vbRsZEojAahxZccgL/PrO+CjakiFFWurg=="
  dependencies:
    "@babel/code-frame" "~7.10.4"
    json5 "^1.0.1"
    write-file-atomic "^2.3.0"

"@expo/plist@0.0.14":
  version "0.0.14"
  resolved "https://registry.npmmirror.com/@expo/plist/download/@expo/plist-0.0.14.tgz"
  integrity "sha1-p1aQO9KKq+CpYSIt8ueFijmiGMk= sha512-bb4Ua1M/OdNgS8KiGdSDUjZ/bbPfv3xdPY/lz8Ctp/adlj/QgB8xA7tVPeqSSfJPZqFRwU0qLCnRhpUOnP51VQ=="
  dependencies:
    "@xmldom/xmldom" "~0.7.0"
    base64-js "^1.2.3"
    xmlbuilder "^14.0.0"

"@hapi/hoek@^9.0.0":
  version "9.2.1"
  resolved "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-9.2.1.tgz"
  integrity "sha1-lVEUKhmAUDdSU2tQUP2Z9KfxOxc= sha512-gfta+H8aziZsm8pZa0vj04KO6biEiisppNgA1kbJvFrrWu9Vm7eaUEy76DIxsuTaWvti5fkJVhllWc6ZTE+Mdw=="

"@hapi/topo@^5.0.0":
  version "5.1.0"
  resolved "https://registry.npmmirror.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz"
  integrity "sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI= sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg=="
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.6.0":
  version "0.6.0"
  resolved "https://registry.npmmirror.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.6.0.tgz"
  integrity sha1-tWIf2zsyMJ0tFldUVsvCd/qPAho=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "https://registry.npmmirror.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "https://registry.npmmirror.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/console/download/@jest/console-27.3.1.tgz?cache=0&sync_timestamp=1634627428059&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fconsole%2Fdownload%2F%40jest%2Fconsole-27.3.1.tgz"
  integrity "sha1-6Oo6R10/gWLyPWnvv6qcvkhr7pM= sha512-RkFNWmv0iui+qsOr/29q9dyfKTTT5DCuP31kUwg7rmOKPT/ozLeGLKJKVIiOfbiKyleUZKIrHwhmiZWVe8IMdw=="
  dependencies:
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^27.3.1"
    jest-util "^27.3.1"
    slash "^3.0.0"

"@jest/core@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/core/download/@jest/core-27.3.1.tgz"
  integrity sha1-BJku8bWLF8RZr7h6tW2B5j04aSU=
  dependencies:
    "@jest/console" "^27.3.1"
    "@jest/reporters" "^27.3.1"
    "@jest/test-result" "^27.3.1"
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.8.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-changed-files "^27.3.0"
    jest-config "^27.3.1"
    jest-haste-map "^27.3.1"
    jest-message-util "^27.3.1"
    jest-regex-util "^27.0.6"
    jest-resolve "^27.3.1"
    jest-resolve-dependencies "^27.3.1"
    jest-runner "^27.3.1"
    jest-runtime "^27.3.1"
    jest-snapshot "^27.3.1"
    jest-util "^27.3.1"
    jest-validate "^27.3.1"
    jest-watcher "^27.3.1"
    micromatch "^4.0.4"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/create-cache-key-function@^27.0.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/create-cache-key-function/download/@jest/create-cache-key-function-27.3.1.tgz?cache=0&sync_timestamp=1634627761890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fcreate-cache-key-function%2Fdownload%2F%40jest%2Fcreate-cache-key-function-27.3.1.tgz"
  integrity sha1-ldDdqPtbNAspOZyh1N4Rt4BA/9k=
  dependencies:
    "@jest/types" "^27.2.5"

"@jest/environment@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/environment/download/@jest/environment-27.3.1.tgz?cache=0&sync_timestamp=1634627425622&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-27.3.1.tgz"
  integrity "sha1-IYLe+86NOF/VHF58cFD1EL1MhrE= sha512-BCKCj4mOVLme6Tanoyc9k0ultp3pnmuyHw73UHRPeeZxirsU/7E3HC4le/VDb/SMzE1JcPnto+XBKFOcoiJzVw=="
  dependencies:
    "@jest/fake-timers" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    jest-mock "^27.3.0"

"@jest/fake-timers@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/fake-timers/download/@jest/fake-timers-27.3.1.tgz?cache=0&sync_timestamp=1634627446944&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Ffake-timers%2Fdownload%2F%40jest%2Ffake-timers-27.3.1.tgz"
  integrity "sha1-H62GDumxMDR2LNuUJm6VYJ385kE= sha512-M3ZFgwwlqJtWZ+QkBG5NmC23A9w+A6ZxNsO5nJxJsKYt4yguBd3i8TpjQz5NfCX91nEve1KqD9RA2Q+Q1uWqoA=="
  dependencies:
    "@jest/types" "^27.2.5"
    "@sinonjs/fake-timers" "^8.0.1"
    "@types/node" "*"
    jest-message-util "^27.3.1"
    jest-mock "^27.3.0"
    jest-util "^27.3.1"

"@jest/globals@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/globals/download/@jest/globals-27.3.1.tgz"
  integrity "sha1-zh37A9N5I3qdpsG5ns+soZIqX54= sha512-Q651FWiWQAIFiN+zS51xqhdZ8g9b88nGCobC87argAxA7nMfNQq0Q0i9zTfQYgLa6qFXk2cGANEqfK051CZ8Pg=="
  dependencies:
    "@jest/environment" "^27.3.1"
    "@jest/types" "^27.2.5"
    expect "^27.3.1"

"@jest/reporters@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/reporters/download/@jest/reporters-27.3.1.tgz"
  integrity "sha1-KLXB9XiUgeI3iASPqCLtFUhkMLk= sha512-m2YxPmL9Qn1emFVgZGEiMwDntDxRRQ2D58tiDQlwYTg5GvbFOKseYCcHtn0WsI8CG4vzPglo3nqbOiT8ySBT/w=="
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^27.3.1"
    "@jest/test-result" "^27.3.1"
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    graceful-fs "^4.2.4"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.3"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.2"
    jest-haste-map "^27.3.1"
    jest-resolve "^27.3.1"
    jest-util "^27.3.1"
    jest-worker "^27.3.1"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^4.0.1"
    terminal-link "^2.0.0"
    v8-to-istanbul "^8.1.0"

"@jest/source-map@^27.0.6":
  version "27.0.6"
  resolved "https://registry.npmmirror.com/@jest/source-map/download/@jest/source-map-27.0.6.tgz"
  integrity "sha1-vp6bk1ZdSbBUi4biMgkkkftgVR8= sha512-Fek4mi5KQrqmlY07T23JRi0e7Z9bXTOOD86V/uS0EIW4PClvPDqZOyFlLpNJheS6QI0FNX1CgmPjtJ4EA/2M+g=="
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.4"
    source-map "^0.6.0"

"@jest/test-result@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/test-result/download/@jest/test-result-27.3.1.tgz?cache=0&sync_timestamp=1634627424292&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Ftest-result%2Fdownload%2F%40jest%2Ftest-result-27.3.1.tgz"
  integrity "sha1-ia3ui3cYd8abO41Z9S8p3MwwAZQ= sha512-mLn6Thm+w2yl0opM8J/QnPTqrfS4FoXsXF2WIWJb2O/GBSyResL71BRuMYbYRsGt7ELwS5JGcEcGb52BNrumgg=="
  dependencies:
    "@jest/console" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/test-sequencer/download/@jest/test-sequencer-27.3.1.tgz"
  integrity "sha1-SzveLbsF7nSv2uYIzwdo4zVGg7E= sha512-siySLo07IMEdSjA4fqEnxfIX8lB/lWYsBPwNFtkOvsFQvmBrL3yj3k3uFNZv/JDyApTakRpxbKLJ3CT8UGVCrA=="
  dependencies:
    "@jest/test-result" "^27.3.1"
    graceful-fs "^4.2.4"
    jest-haste-map "^27.3.1"
    jest-runtime "^27.3.1"

"@jest/transform@^27.3.1":
  version "27.3.1"
  resolved "https://registry.npmmirror.com/@jest/transform/download/@jest/transform-27.3.1.tgz"
  integrity sha1-/4Dq++q+gR6QJeS29FISZxhFUiA=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^27.2.5"
    babel-plugin-istanbul "^6.0.0"
    chalk "^4.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^27.3.1"
    jest-regex-util "^27.0.6"
    jest-util "^27.3.1"
    micromatch "^4.0.4"
    pirates "^4.0.1"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^26.6.2":
  version "26.6.2"
  resolved "https://registry.npmmirror.com/@jest/types/download/@jest/types-26.6.2.tgz"
  integrity "sha1-vvWlMgMOHYii9abZM/hOlyJu1I4= sha512-fC6QCp7Sc5sX6g8Tvbmj4XUTbyrik0akgRy03yjXbQaBWWNWGE7SGtJk98m0N8nzegD/7SggrUlivxo5ax4KWQ=="
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    chalk "^4.0.0"

"@jest/types@^27.2.5":
  version "27.2.5"
  resolved "https://registry.npmmirror.com/@jest/types/download/@jest/types-27.2.5.tgz"
  integrity sha1-QgdlwFJgXnVoaYLSSwYbTLuiITI=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"

"@jridgewell/gen-mapping@^0.3.2":
  version "0.3.2"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz"
  integrity sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  version "3.1.0"
  resolved "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz"
  integrity sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==

"@jridgewell/set-array@^1.0.1":
  version "1.1.2"
  resolved "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz"
  integrity sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==

"@jridgewell/sourcemap-codec@1.4.14", "@jridgewell/sourcemap-codec@^1.4.10":
  version "1.4.14"
  resolved "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz"
  integrity sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==

"@jridgewell/trace-mapping@^0.3.9":
  version "0.3.17"
  resolved "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz"
  integrity sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmmirror.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@react-native-async-storage/async-storage@^1.15.11":
  version "1.15.11"
  resolved "https://registry.npmmirror.com/@react-native-async-storage/async-storage/download/@react-native-async-storage/async-storage-1.15.11.tgz"
  integrity sha1-2mJbkGUpB2QUwy/TN9gTtWUUmFg=
  dependencies:
    merge-options "^3.0.4"

"@react-native-community/cameraroll@^4.1.2":
  version "4.1.2"
  resolved "https://registry.npmmirror.com/@react-native-community/cameraroll/download/@react-native-community/cameraroll-4.1.2.tgz"
  integrity sha1-SJxrthN1cVQNk8VD1fz4xlK1SOw=

"@react-native-community/cli-debugger-ui@^6.0.0-rc.0":
  version "6.0.0-rc.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-debugger-ui/download/@react-native-community/cli-debugger-ui-6.0.0-rc.0.tgz"
  integrity "sha1-d0N4Ym5LcPXh4uVJEEcty6/6FTY= sha512-achYcPPoWa9D02C5tn6TBzjeY443wQTyx37urptc75JpZ7gR5YHsDyIEEWa3DDYp1va9zx/iGg+uZ/hWw07GAw=="
  dependencies:
    serve-static "^1.13.1"

"@react-native-community/cli-hermes@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-hermes/download/@react-native-community/cli-hermes-6.1.0.tgz?cache=0&sync_timestamp=1633349772330&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-hermes%2Fdownload%2F%40react-native-community%2Fcli-hermes-6.1.0.tgz"
  integrity "sha1-9onxtP7EUQy6BjFEtg2oT/YjWFg= sha512-BJyzGlUqnggbBL4Vh4cIC08oKOK4PoelxZFEo7TjFjfdBKvbM6955JN77ExJ7IdeLuGVpY4vaMwAJdx5l7LxKg=="
  dependencies:
    "@react-native-community/cli-platform-android" "^6.1.0"
    "@react-native-community/cli-tools" "^6.1.0"
    chalk "^3.0.0"
    hermes-profile-transformer "^0.0.6"
    ip "^1.1.5"

"@react-native-community/cli-platform-android@^6.0.0", "@react-native-community/cli-platform-android@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-platform-android/download/@react-native-community/cli-platform-android-6.1.0.tgz?cache=0&sync_timestamp=1633349764886&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-platform-android%2Fdownload%2F%40react-native-community%2Fcli-platform-android-6.1.0.tgz"
  integrity sha1-jIU4B8jBUmWTtzXooZ3B9Ecd5p4=
  dependencies:
    "@react-native-community/cli-tools" "^6.1.0"
    chalk "^3.0.0"
    execa "^1.0.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    jetifier "^1.6.2"
    lodash "^4.17.15"
    logkitty "^0.7.1"
    slash "^3.0.0"
    xmldoc "^1.1.2"

"@react-native-community/cli-platform-ios@^6.0.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-platform-ios/download/@react-native-community/cli-platform-ios-6.1.0.tgz?cache=0&sync_timestamp=1633349770978&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-platform-ios%2Fdownload%2F%40react-native-community%2Fcli-platform-ios-6.1.0.tgz"
  integrity sha1-NpzZNraJr5PQcPUt8nlplCI+dwU=
  dependencies:
    "@react-native-community/cli-tools" "^6.1.0"
    chalk "^3.0.0"
    glob "^7.1.3"
    js-yaml "^3.13.1"
    lodash "^4.17.15"
    plist "^3.0.2"
    xcode "^2.0.0"

"@react-native-community/cli-plugin-metro@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-plugin-metro/download/@react-native-community/cli-plugin-metro-6.1.0.tgz"
  integrity "sha1-PzRzKjamg8Iec4Q95JsTRS7WOwY= sha512-ltHJquEgA6H4OTIUqWIkNm/xxAB9D4DK2K9M0jie9FfkOoqBXA7QS2WnC8GEa6a+3VIDwevB0RJsch218FdZCw=="
  dependencies:
    "@react-native-community/cli-server-api" "^6.1.0"
    "@react-native-community/cli-tools" "^6.1.0"
    chalk "^3.0.0"
    metro "^0.66.1"
    metro-config "^0.66.1"
    metro-core "^0.66.1"
    metro-react-native-babel-transformer "^0.66.1"
    metro-resolver "^0.66.1"
    metro-runtime "^0.66.1"
    mkdirp "^0.5.1"
    readline "^1.3.0"

"@react-native-community/cli-server-api@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-server-api/download/@react-native-community/cli-server-api-6.1.0.tgz?cache=0&sync_timestamp=1633349771866&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-server-api%2Fdownload%2F%40react-native-community%2Fcli-server-api-6.1.0.tgz"
  integrity "sha1-E4OQUnM/7G7mVkJc1EzjkGTISvE= sha512-WEJzdoF4JNUogZAd+Gdgbr+D/S/PHGjxH+PDjk3ST9pAUxEHb6naNwEl5dSJUY/ecBV63latNZkKunRyvFAx9A=="
  dependencies:
    "@react-native-community/cli-debugger-ui" "^6.0.0-rc.0"
    "@react-native-community/cli-tools" "^6.1.0"
    compression "^1.7.1"
    connect "^3.6.5"
    errorhandler "^1.5.0"
    nocache "^2.1.0"
    pretty-format "^26.6.2"
    serve-static "^1.13.1"
    ws "^1.1.0"

"@react-native-community/cli-tools@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-tools/download/@react-native-community/cli-tools-6.1.0.tgz?cache=0&sync_timestamp=1633349770140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-tools%2Fdownload%2F%40react-native-community%2Fcli-tools-6.1.0.tgz"
  integrity "sha1-+xa45IBdrfTSpXxp/+g92t/753E= sha512-MT8syhvk0vpfyYyHlcDoGicKcqMtBN7WPmDeyW16u+eKBtw/+EKq+86cFCuOHCfHK20ujG1mZqA1txxlCbu8GA=="
  dependencies:
    appdirsjs "^1.2.4"
    chalk "^3.0.0"
    lodash "^4.17.15"
    mime "^2.4.1"
    mkdirp "^0.5.1"
    node-fetch "^2.6.0"
    open "^6.2.0"
    semver "^6.3.0"
    shell-quote "1.6.1"

"@react-native-community/cli-types@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli-types/download/@react-native-community/cli-types-6.0.0.tgz"
  integrity "sha1-kCafvccinV47jy8+ApqUCDVRBA0= sha512-K493Fk2DMJC0ZM8s8gnfseKxGasIhuDaCUDeLZcoCSFlrjKEuEs1BKKEJiev0CARhKEXKOyyp/uqYM9nWhisNw=="
  dependencies:
    ora "^3.4.0"

"@react-native-community/cli@^6.0.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/cli/download/@react-native-community/cli-6.1.0.tgz?cache=0&sync_timestamp=1633349765333&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli%2Fdownload%2F%40react-native-community%2Fcli-6.1.0.tgz"
  integrity sha1-5KrVJAcVpYelHX93n2DSAZnjR84=
  dependencies:
    "@react-native-community/cli-debugger-ui" "^6.0.0-rc.0"
    "@react-native-community/cli-hermes" "^6.1.0"
    "@react-native-community/cli-plugin-metro" "^6.1.0"
    "@react-native-community/cli-server-api" "^6.1.0"
    "@react-native-community/cli-tools" "^6.1.0"
    "@react-native-community/cli-types" "^6.0.0"
    appdirsjs "^1.2.4"
    chalk "^3.0.0"
    command-exists "^1.2.8"
    commander "^2.19.0"
    cosmiconfig "^5.1.0"
    deepmerge "^3.2.0"
    envinfo "^7.7.2"
    execa "^1.0.0"
    find-up "^4.1.0"
    fs-extra "^8.1.0"
    glob "^7.1.3"
    graceful-fs "^4.1.3"
    joi "^17.2.1"
    leven "^3.1.0"
    lodash "^4.17.15"
    minimist "^1.2.0"
    mkdirp "^0.5.1"
    node-stream-zip "^1.9.1"
    ora "^3.4.0"
    pretty-format "^26.6.2"
    prompts "^2.4.0"
    semver "^6.3.0"
    serve-static "^1.13.1"
    strip-ansi "^5.2.0"
    sudo-prompt "^9.0.0"
    wcwidth "^1.0.1"

"@react-native-community/eslint-config@^3.0.1":
  version "3.0.1"
  resolved "https://registry.npmmirror.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-3.0.1.tgz"
  integrity sha1-xFlo8SFBOf50enrr26BsT6Kk0Bg=
  dependencies:
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^4.22.1"
    "@typescript-eslint/parser" "^4.22.1"
    babel-eslint "^10.1.0"
    eslint-config-prettier "^6.10.1"
    eslint-plugin-eslint-comments "^3.1.2"
    eslint-plugin-flowtype "2.50.3"
    eslint-plugin-jest "22.4.1"
    eslint-plugin-prettier "3.1.2"
    eslint-plugin-react "^7.20.0"
    eslint-plugin-react-hooks "^4.0.7"
    eslint-plugin-react-native "^3.10.0"
    prettier "^2.0.2"

"@react-native-community/eslint-plugin@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmmirror.com/@react-native-community/eslint-plugin/download/@react-native-community/eslint-plugin-1.1.0.tgz"
  integrity sha1-5Csb7xLSQVQRUZ/VKOZLWTsTY9w=

"@react-native-community/masked-view@^0.1.11":
  version "0.1.11"
  resolved "https://registry.npmmirror.com/@react-native-community/masked-view/download/@react-native-community/masked-view-0.1.11.tgz"
  integrity sha1-L0xuEL7geGq/9GBOOaN97W85gM4=

"@react-native-community/segmented-control@^2.2.2":
  version "2.2.2"
  resolved "https://registry.npmmirror.com/@react-native-community/segmented-control/download/@react-native-community/segmented-control-2.2.2.tgz"
  integrity sha1-QBQlaBmrj0D2vDo5Kf8UqdFJzwQ=

"@react-native-community/slider@^4.1.11":
  version "4.1.11"
  resolved "https://registry.npmmirror.com/@react-native-community/slider/download/@react-native-community/slider-4.1.11.tgz"
  integrity sha1-OQyHDaF28RBBLJdvJTeaPxha/hU=
  dependencies:
    flow-bin "0.113.0"

"@react-native-picker/picker@^2.2.0":
  version "2.2.0"
  resolved "https://registry.npmmirror.com/@react-native-picker/picker/download/@react-native-picker/picker-2.2.0.tgz"
  integrity sha1-6W7MCAi98qcGomO0IoZL7loOa3Y=

"@react-native/assets@1.0.0":
  version "1.0.0"
  resolved "https://registry.npmmirror.com/@react-native/assets/download/@react-native/assets-1.0.0.tgz"
  integrity sha1-xvm/Y9J0uvyOlwYo3iSYazClXI4=

"@react-native/normalize-color@1.0.0":
  version "1.0.0"
  resolved "https://registry.npmmirror.com/@react-native/normalize-color/download/@react-native/normalize-color-1.0.0.tgz"
  integrity sha1-xSqZ1P4BBJEC1H3EXUDL3k9yCrY=

"@react-native/polyfills@2.0.0":
  version "2.0.0"
  resolved "https://registry.npmmirror.com/@react-native/polyfills/download/@react-native/polyfills-2.0.0.tgz"
  integrity sha1-TEC3RlXIOYLIz0dTDufcE9lXtqo=

"@react-navigation/core@^3.7.9":
  version "3.7.9"
  resolved "https://registry.npmmirror.com/@react-navigation/core/download/@react-navigation/core-3.7.9.tgz"
  integrity sha1-P3ug/LbI10p3oFc4KvGY2Ex8Tjs=
  dependencies:
    hoist-non-react-statics "^3.3.2"
    path-to-regexp "^1.8.0"
    query-string "^6.13.6"
    react-is "^16.13.0"

"@react-navigation/native@^3.8.4":
  version "3.8.4"
  resolved "https://registry.npmmirror.com/@react-navigation/native/download/@react-navigation/native-3.8.4.tgz"
  integrity sha1-TXf4ZQY2Ts8Yszx/h0CvtnY9Czc=
  dependencies:
    hoist-non-react-statics "^3.3.2"
    react-native-safe-area-view "^0.14.9"

"@sideway/address@^4.1.0":
  version "4.1.2"
  resolved "https://registry.npmmirror.com/@sideway/address/download/@sideway/address-4.1.2.tgz"
  integrity "sha1-gRuEMzozVznTlpz8Q0c2JoFwytE= sha512-idTz8ibqWFrPU8kMirL0CoPH/A29XOzzAzpyN3zQ4kAWnzmNfFmRaoMNN6VI8ske5M73HZyhIaW4OuSFIdM4oA=="
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.0":
  version "3.0.0"
  resolved "https://registry.npmmirror.com/@sideway/formula/download/@sideway/formula-3.0.0.tgz"
  integrity "sha1-/hWK7jLmvV3oUES+YVvAhHigoTw= sha512-vHe7wZ4NOXVfkoRb8T5otiENVlT7a3IAiw7H5M2+GO+9CDgcVUUsX1zalAztCmwyOr2RUTGJdgB+ZvSVqmdHmg=="

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmmirror.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz"
  integrity "sha1-z/j/rcNyrSn9P3gneusp5jLMcN8= sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ=="

"@sinonjs/commons@^1.7.0":
  version "1.8.3"
  resolved "https://registry.npmmirror.com/@sinonjs/commons/download/@sinonjs/commons-1.8.3.tgz"
  integrity sha1-OALd0hpQqUm2ch3dcto25n5/Gy0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^8.0.1":
  version "8.1.0"
  resolved "https://registry.npmmirror.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-8.1.0.tgz?cache=0&sync_timestamp=1635949916472&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40sinonjs%2Ffake-timers%2Fdownload%2F%40sinonjs%2Ffake-timers-8.1.0.tgz"
  integrity sha1-P9wrbLWJNbIb+40WJesTAEhDFuc=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "https://registry.npmmirror.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.14":
  version "7.1.16"
  resolved "https://registry.npmmirror.com/@types/babel__core/download/@types/babel__core-7.1.16.tgz"
  integrity sha1-vBLHS31l6C0ph2tdC69cYlrFhwI=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.3"
  resolved "https://registry.npmmirror.com/@types/babel__generator/download/@types/babel__generator-7.6.3.tgz"
  integrity "sha1-9Fa0ss55E392iqEw0kI9LwzPq6U= sha512-/GWCmzJWqV7diQW54smJZzWbSFf4QYtF71WCKhcx6Ru/tFyQIY2eiiITcCAeuPbNSvT9YCGkVMqqvSk2Z0mXiA=="
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.1"
  resolved "https://registry.npmmirror.com/@types/babel__template/download/@types/babel__template-7.4.1.tgz"
  integrity sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  version "7.14.2"
  resolved "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.14.2.tgz"
  integrity sha1-/81HC7s/i/MEgWePtVAieMqDOkM=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/graceful-fs@^4.1.2":
  version "4.1.5"
  resolved "https://registry.npmmirror.com/@types/graceful-fs/download/@types/graceful-fs-4.1.5.tgz"
  integrity sha1-If+6DZjaQ1DbZIkfkqnl2zzbThU=
  dependencies:
    "@types/node" "*"

"@types/hammerjs@^2.0.36":
  version "2.0.40"
  resolved "https://registry.npmmirror.com/@types/hammerjs/download/@types/hammerjs-2.0.40.tgz"
  integrity "sha1-3tAkC26hrXr8HmA3TEkIeq6l29g= sha512-VbjwR1fhsn2h2KXAY4oy1fm7dCxaKy0D+deTb8Ilc3Eo3rc5+5eA4rfYmZaHgNJKxVyI0f6WIXzO2zLkVmQPHA=="

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.3"
  resolved "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz"
  integrity "sha1-S6jdtyAiH0MuRDvV+RF/0iz9R2I= sha512-sz7iLqvVUg1gIedBOvlkxPlc8/uVzyS5OwGz1cKjXzkl3FpL3al0crU8YGU1WoHkxn0Wxbw5tyi6hvzJKNzFsw=="

"@types/istanbul-lib-report@*":
  version "3.0.0"
  resolved "https://registry.npmmirror.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.1"
  resolved "https://registry.npmmirror.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz"
  integrity sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/json-schema@^7.0.7":
  version "7.0.9"
  resolved "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz?cache=0&sync_timestamp=1632763827525&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.9.tgz"
  integrity sha1-l+3JA36gw4WFMgsolk3eOznkZg0=

"@types/node@*":
  version "16.11.6"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz"
  integrity sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64=

"@types/prettier@^2.1.5":
  version "2.4.1"
  resolved "https://registry.npmmirror.com/@types/prettier/download/@types/prettier-2.4.1.tgz"
  integrity sha1-4TAwSNU4lWPhMPW92J03qZrLdes=

"@types/shallowequal@^1.1.1":
  version "1.1.1"
  resolved "https://registry.npmmirror.com/@types/shallowequal/download/@types/shallowequal-1.1.1.tgz"
  integrity sha1-qtJiuz8rElfZTHHVRSaNWSV1ybE=

"@types/stack-utils@^2.0.0":
  version "2.0.1"
  resolved "https://registry.npmmirror.com/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz"
  integrity sha1-IPGClPeX8iCbX2XI47XI6CYdEnw=

"@types/yargs-parser@*":
  version "20.2.1"
  resolved "https://registry.npmmirror.com/@types/yargs-parser/download/@types/yargs-parser-20.2.1.tgz"
  integrity sha1-O5ziSJkZ2eT+pDm3aRarw0st8Sk=

"@types/yargs@^15.0.0":
  version "15.0.14"
  resolved "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-15.0.14.tgz"
  integrity "sha1-Jtgh3biecEkhYLZtEKDrbfj2+wY= sha512-yEJzHoxf6SyQGhBhIYGXQDSCkJjB6HohDShto7m8vaKg9Yp0Yn8+71J9eakh2bnPg6BfsH9PRMhiRTZnd4eXGQ=="
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^16.0.0":
  version "16.0.4"
  resolved "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-16.0.4.tgz"
  integrity sha1-JqrZjdLCo45CEIbqmtQrnlFkKXc=
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^4.22.1":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-4.33.0.tgz"
  integrity sha1-wk3HyAacdwa8QNmfb6h+3LIAUnY=
  dependencies:
    "@typescript-eslint/experimental-utils" "4.33.0"
    "@typescript-eslint/scope-manager" "4.33.0"
    debug "^4.3.1"
    functional-red-black-tree "^1.0.1"
    ignore "^5.1.8"
    regexpp "^3.1.0"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-4.33.0.tgz"
  integrity "sha1-byp4akIJ+iIimJ6TgLUzGygQ9/0= sha512-zeQjOoES5JFjTnAhI5QY7ZviczMzDptls15GFsI6jyUOq0kOf9+WonkhtlIhh0RgHRnqj5gdNxW5j1EvAyYg6Q=="
  dependencies:
    "@types/json-schema" "^7.0.7"
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/parser@^4.22.1":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/parser/download/@typescript-eslint/parser-4.33.0.tgz"
  integrity sha1-3+eXVw2WlOVgUo0Y7srYbIx0SJk=
  dependencies:
    "@typescript-eslint/scope-manager" "4.33.0"
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/typescript-estree" "4.33.0"
    debug "^4.3.1"

"@typescript-eslint/scope-manager@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.33.0.tgz"
  integrity "sha1-045JKA2YPody4pEhz4xukiHygKM= sha512-5IfJHpgTsTZuONKbODctL4kKuQje/bzBRkwHE8UOZ4f89Zeddg+EGZs8PD8NcN4LdM3ygHWYB3ukPAYjvl/qbQ=="
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"

"@typescript-eslint/types@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/types/download/@typescript-eslint/types-4.33.0.tgz"
  integrity "sha1-oeWQNqO1OuhDDO6/KpGdx/mvbXI= sha512-zKp7CjQzLQImXEpLt2BUw1tvOMPfNoTAfb8l51evhYbOEEzdWyQNmHWWGPR6hwKJDAi+1VXSBmnhL9kyVTTOuQ=="

"@typescript-eslint/typescript-estree@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.33.0.tgz"
  integrity "sha1-DftRwpCPaMXAjYKu/q8WahfCRgk= sha512-rkWRY1MPFzjwnEVHsxGemDzqqddw2QbTJlICPD9p9I9LfsO8fdmfQPOX3uKfUaGRDFJbfrtm/sXhVXN4E+bzCA=="
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    "@typescript-eslint/visitor-keys" "4.33.0"
    debug "^4.3.1"
    globby "^11.0.3"
    is-glob "^4.0.1"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/visitor-keys@4.33.0":
  version "4.33.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.33.0.tgz"
  integrity "sha1-KiL3ekFgQom3oYZYbp7EjKku8d0= sha512-uqi/2aSz9g2ftcHWf8uLPJA70rUv6yuMW5Bohw+bwcuzaxQIHaKFZCKGoGXIrc9vkTJ3+0txM73K0Hq3d5wgIg=="
  dependencies:
    "@typescript-eslint/types" "4.33.0"
    eslint-visitor-keys "^2.0.0"

"@xmldom/xmldom@~0.7.0":
  version "0.7.5"
  resolved "https://registry.npmmirror.com/@xmldom/xmldom/download/@xmldom/xmldom-0.7.5.tgz"
  integrity "sha1-CfpR41bQfQviAGQrDk+R2ObdQI0= sha512-V3BIhmY36fXZ1OtVcI9W+FxQqxVLsPKcNjWigIaa81dLC9IolJl5Mt4Cvhmr0flUnjSpTdrbMTSbXqYqV5dT6A=="

abab@^2.0.3, abab@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/abab/download/abab-2.0.5.tgz"
  integrity sha1-wLZ4+zLWD8EhnHhNaoJv44Wut5o=

abort-controller@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/abort-controller/download/abort-controller-3.0.0.tgz"
  integrity sha1-6vVNU7YrrkE46AnKIlyEOabvs5I=
  dependencies:
    event-target-shim "^5.0.0"

absolute-path@^0.0.0:
  version "0.0.0"
  resolved "https://registry.npmmirror.com/absolute-path/download/absolute-path-0.0.0.tgz"
  integrity "sha1-p4di+9rftSl76ZsV01p4Wy8JW/c= sha512-HQiug4c+/s3WOvEnDRxXVmNtSG5s2gJM9r19BTcqjp7BWcE48PB+Y2G6jE65kqI0LpsQeMZygt/b60Gi4KxGyA=="

accepts@^1.3.7, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/acorn-globals/download/acorn-globals-6.0.0.tgz"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "https://registry.npmmirror.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^7.1.1:
  version "7.4.1"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.2.4, acorn@^8.5.0:
  version "8.5.0"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-8.5.0.tgz"
  integrity "sha1-RRLMuZs2mMdSWR6btEcuOK1DzuI= sha512-yXbYeFy+jUuYd3/CDcg2NkIYE991XYX/bje7LmjJigUciaeO1JR4XxXgCIV1/Zc/dRuFEyw1L0pbA+qynJkW5Q=="

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/agent-base/download/agent-base-6.0.2.tgz"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

ajv@^6.10.0, ajv@^6.12.4:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1632363896657&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

anser@^1.4.9:
  version "1.4.10"
  resolved "https://registry.npmmirror.com/anser/download/anser-1.4.10.tgz?cache=0&sync_timestamp=1634204728307&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fanser%2Fdownload%2Fanser-1.4.10.tgz"
  integrity sha1-vvo+3fKCaEvQO2Pc2jknrvjC41s=

ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/ansi-colors/download/ansi-colors-4.1.1.tgz"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-fragments@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/ansi-fragments/download/ansi-fragments-0.2.1.tgz"
  integrity "sha1-JECcVsTMN4F8PXyqmdiWni3loF4= sha512-DykbNHxuXQwUDRv5ibc2b0x7uw7wmwOGLBUd5RmaQ5z8Lhx19vwvKV+FAsM5rEA6dEcHxX+/Ad5s9eF2k2bB+w=="
  dependencies:
    colorette "^1.0.7"
    slice-ansi "^2.0.0"
    strip-ansi "^5.0.0"

ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-4.1.0.tgz"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0, ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-5.2.0.tgz"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/anymatch/download/anymatch-2.0.0.tgz"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.3:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.2.tgz"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

appdirsjs@^1.2.4:
  version "1.2.6"
  resolved "https://registry.npmmirror.com/appdirsjs/download/appdirsjs-1.2.6.tgz"
  integrity "sha1-/M+e5UMxVJKGfKz8/UorMiV9MKw= sha512-D8wJNkqMCeQs3kLasatELsddox/Xqkhp+J07iXGyL54fVN7oc+nmNfYzGuCs1IEP6uBw+TfpuO3JKwc+lECy4w=="

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/argparse/download/argparse-2.0.1.tgz"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-filter@~0.0.0:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/array-filter/download/array-filter-0.0.1.tgz"
  integrity "sha1-fajPLiZijtcygDWB/SH2fKzS7uw= sha512-VW0FpCIhjZdarWjIz8Vpva7U95fl2Jn+b+mmFFMLn8PIVscOQcAgEznwUzTEuUHuqZqIxwzRlcaN/urTFFQoiw=="

array-includes@^3.1.3:
  version "3.1.4"
  resolved "https://registry.npmmirror.com/array-includes/download/array-includes-3.1.4.tgz?cache=0&sync_timestamp=1633411730863&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Farray-includes%2Fdownload%2Farray-includes-3.1.4.tgz"
  integrity sha1-9bSTFix2DzU5Yx8AW6K7Rqy0W6k=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    is-string "^1.0.7"

array-map@~0.0.0:
  version "0.0.0"
  resolved "https://registry.npmmirror.com/array-map/download/array-map-0.0.0.tgz"
  integrity "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI= sha512-123XMszMB01QKVptpDQ7x1m1pP5NmJIG1kbl0JSPPRezvwQChxAN0Gvzo7rvR1IZ2tOL2tmiy7kY/KKgnpVVpg=="

array-reduce@~0.0.0:
  version "0.0.0"
  resolved "https://registry.npmmirror.com/array-reduce/download/array-reduce-0.0.0.tgz"
  integrity "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys= sha512-8jR+StqaC636u7h3ye1co3lQRefgVVUQUhuAmRbDqIMeR2yuXzRvkCNQiQ5J/wbREmoBLNtp13dhaaVpZQDRUw=="

array-tree-filter@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/array-tree-filter/download/array-tree-filter-2.1.0.tgz"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/array-union/download/array-union-2.1.0.tgz"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

array.prototype.flatmap@^1.2.4:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/array.prototype.flatmap/download/array.prototype.flatmap-1.2.5.tgz"
  integrity sha1-kI3ILYpAaTD984WY1R50EdGNREY=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    es-abstract "^1.19.0"

asap@~2.0.3, asap@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/asap/download/asap-2.0.6.tgz"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

ast-types@0.14.2:
  version "0.14.2"
  resolved "https://registry.npmmirror.com/ast-types/download/ast-types-0.14.2.tgz"
  integrity "sha1-YAuILfhYPjzU8t9fog+oN1nUvf0= sha512-O0yuUDnZeQDL+ncNGlJ78BiO4jnYI3bvMsD5prT0/nsgijG/LpNBIr63gTjVTNsiGkgQhiyCShTgxt8oXOrklA=="
  dependencies:
    tslib "^2.0.1"

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/astral-regex/download/astral-regex-1.0.0.tgz"
  integrity "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k= sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg=="

async-limiter@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/async-limiter/download/async-limiter-1.0.1.tgz"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

async@^2.4.0:
  version "2.6.3"
  resolved "https://registry.npmmirror.com/async/download/async-2.6.3.tgz"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/at-least-node/download/at-least-node-1.0.0.tgz"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

babel-core@^7.0.0-bridge.0:
  version "7.0.0-bridge.0"
  resolved "https://registry.npmmirror.com/babel-core/download/babel-core-7.0.0-bridge.0.tgz"
  integrity "sha1-laSS3dkPm06aSh2hTrM1uHtjTs4= sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg=="

babel-eslint@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmmirror.com/babel-eslint/download/babel-eslint-10.1.0.tgz"
  integrity sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-jest@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/babel-jest/download/babel-jest-27.3.1.tgz?cache=0&sync_timestamp=1634626745680&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-jest%2Fdownload%2Fbabel-jest-27.3.1.tgz"
  integrity sha1-BjajQExo4HAB5DSsSVbYLaioACI=
  dependencies:
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^27.2.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    slash "^3.0.0"

babel-plugin-import@^1.13.3:
  version "1.13.3"
  resolved "https://registry.npmmirror.com/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz"
  integrity sha1-nbu6fRrHK9QSkXqDDUReAJQdJtc=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-plugin-istanbul@^6.0.0:
  version "6.1.1"
  resolved "https://registry.npmmirror.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^27.2.0:
  version "27.2.0"
  resolved "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-27.2.0.tgz"
  integrity "sha1-efN9Q/flxP3Esso+EMxs9UViYnc= sha512-TOux9khNKdi64mW+0OIhcmbAn75tTlzKhxmiNXevQaPbrBYK7YKjP1jl6NHTJ6XR5UgUrJbCnWlKVnJn29dfjw=="
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

babel-plugin-module-resolver@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-4.1.0.tgz"
  integrity sha1-IqTzL3RBcn7B+/SWe4Y+Hj6fM+I=
  dependencies:
    find-babel-config "^1.2.0"
    glob "^7.1.6"
    pkg-up "^3.1.0"
    reselect "^4.0.0"
    resolve "^1.13.1"

babel-plugin-polyfill-corejs2@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.2.3.tgz"
  integrity "sha1-btjjCYGwYvj+asqIc6N+vMjMHA8= sha512-NDZ0auNRzmAfE1oDDPW2JhzIMXUk+FFe2ICejmt5T4ocKgiQx3e0VCRx9NCAidcMtL2RUZaWtXnmjTCkx0tcbA=="
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.2.4"
    semver "^6.1.1"

babel-plugin-polyfill-corejs3@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.3.0.tgz?cache=0&sync_timestamp=1635567635564&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.3.0.tgz"
  integrity "sha1-+nyj0e6d3GGTYA/7YyyXhdVJGK8= sha512-JLwi9vloVdXLjzACL80j24bG6/T1gYxwowG44dg6HN/7aTPdyPbJJidf6ajoA3RPHHtW0j9KMrSOLpIZpAnPpg=="
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.4"
    core-js-compat "^3.18.0"

babel-plugin-polyfill-regenerator@^0.2.3:
  version "0.2.3"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.2.3.tgz?cache=0&sync_timestamp=1635567640308&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.2.3.tgz"
  integrity "sha1-LpgI9QJ8QzbJlJkrSKQmJYDLjW0= sha512-JVE78oRZPKFIeUqFGrSORNzQnrDwZR16oiWeGM8ZyjBn2XAT5OjP+wXx5ESuo33nUsFUEJYjtklnsKbxW5L+7g=="
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.2.4"

babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0:
  version "7.0.0-beta.0"
  resolved "https://registry.npmmirror.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz"
  integrity "sha1-qiE8FDXiv/62/KhCKH71NK0F1c8= sha512-Xj9XuRuz3nTSbaTXWv3itLOcxyF4oPD8douBBmj7U9BBC6nEBYfyOJYQMf/8PJAFotC62UY5dFfIGEPr7WswzQ=="

babel-preset-current-node-syntax@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz"
  integrity sha1-tDmSObibKgEfndvj5PQB/EDP9zs=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

babel-preset-fbjs@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/babel-preset-fbjs/download/babel-preset-fbjs-3.4.0.tgz"
  integrity "sha1-OKFOWno7KFo/OoZVLWUNylz2ERw= sha512-9ywCsCvo1ojrw0b+XYk7aFvTH6D9064t0RIL1rtMf3nsa02Xw41MS7sZw216Im35xj/UY0PDBQsa1brUDDF1Ow=="
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    babel-plugin-syntax-trailing-function-commas "^7.0.0-beta.0"

babel-preset-jest@^27.2.0:
  version "27.2.0"
  resolved "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-27.2.0.tgz"
  integrity sha1-VWu780Bgj+1WcKsOoMjvJEn7qIU=
  dependencies:
    babel-plugin-jest-hoist "^27.2.0"
    babel-preset-current-node-syntax "^1.0.0"

babel-runtime@^6.x:
  version "6.26.0"
  resolved "https://registry.npmmirror.com/babel-runtime/download/babel-runtime-6.26.0.tgz"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.1.2, base64-js@^1.2.3, base64-js@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npmmirror.com/base/download/base-0.11.2.tgz"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

big-integer@1.6.x:
  version "1.6.50"
  resolved "https://registry.npmmirror.com/big-integer/download/big-integer-1.6.50.tgz?cache=0&sync_timestamp=1634050154371&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbig-integer%2Fdownload%2Fbig-integer-1.6.50.tgz"
  integrity "sha1-KZpL6L1EHHPcxJLtRrcWnDTpLnA= sha512-+O2uoQWFRo8ysZNo/rjtri2jIwjr3XfeAgRjAUADRqGG+ZITvyn8J1kvXLTaKVr3hhGXk+f23tKfdzmklVM9vQ=="

bplist-creator@0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/bplist-creator/download/bplist-creator-0.1.0.tgz"
  integrity "sha1-AYotG1h/dp43nvVRkQNzD4ljuh4= sha512-sXaHZicyEEmY86WyueLTQesbeoH/mquvarJaQNbjuOQO+7gbFcDEWqKmcWA4cOTLzFlfgvkiVxolk1k5bBIpmg=="
  dependencies:
    stream-buffers "2.2.x"

bplist-parser@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/bplist-parser/download/bplist-parser-0.3.0.tgz"
  integrity "sha1-ulBmY3D2G7+UiBY2zZ99I8UoYJA= sha512-zgmaRvT6AN1JpPPV+S0a1/FAtoxSreYDccZGIqEMSvZl9DMe70mJ7MFzpxa1X+gHVdkToE2haRUHHMiW1OdejA=="
  dependencies:
    big-integer "1.6.x"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

browserslist@^4.21.3, browserslist@^4.21.4:
  version "4.21.4"
  resolved "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.4.tgz"
  integrity sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==
  dependencies:
    caniuse-lite "^1.0.30001400"
    electron-to-chromium "^1.4.251"
    node-releases "^2.0.6"
    update-browserslist-db "^1.0.9"

bser@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/bser/download/bser-2.1.1.tgz"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.2.tgz"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

builtin-modules@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/builtin-modules/download/builtin-modules-1.1.1.tgz"
  integrity sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.0, call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1633616961620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz"
  integrity "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ= sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ=="
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz?cache=0&sync_timestamp=1633674209796&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-path%2Fdownload%2Fcaller-path-2.0.0.tgz"
  integrity "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ= sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A=="
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/callsites/download/callsites-2.0.0.tgz"
  integrity "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA= sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ=="

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.0.0, camelcase@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.0.tgz"
  integrity "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk= sha512-c7wVvbw3f37nuobQNtgsgG9POC9qMbNuMQmTCqZv23b6MIz0fcYpBiOlv9gEN/hdLdnZTDQhg6e9Dq5M1vKvfg=="

caniuse-lite@^1.0.30001400:
  version "1.0.30001434"
  resolved "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001434.tgz"
  integrity sha512-aOBHrLmTQw//WFa2rcF1If9fa3ypkC1wzqqiKHgfdrXTWcU8C4gKVZT77eQAPWN1APys3+uQ0Df07rKauXGEYA==

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/capture-exit/download/capture-exit-2.0.0.tgz"
  integrity "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ= sha512-PiT/hQmTonHhl/HFGN+Lx3JJUznrVYJ3+AQsnthneZbvW7x+f08Tk7yLJTLEOUvBTbduLeeBkxEaYXUOUrRq6g=="
  dependencies:
    rsvp "^4.8.4"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.3.0, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/char-regex/download/char-regex-1.0.2.tgz"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

ci-info@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/ci-info/download/ci-info-3.2.0.tgz"
  integrity "sha1-KHbLlIpJh5e1I28AlbwFfQ3KOLY= sha512-dVqRX7fLUm8J6FgHJ418XuIgDLZDkYcDFTeL6TA2gt5WlIZUQrrH6EZrNClwT/H0FateUsZkGIOPRrLbP+PR9A=="

cjs-module-lexer@^1.0.0:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/cjs-module-lexer/download/cjs-module-lexer-1.2.2.tgz"
  integrity sha1-n4S6MkSlEvOlTlJ36O70xImGTkA=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-spinners@^2.0.0:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.6.1.tgz?cache=0&sync_timestamp=1633109592807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.6.1.tgz"
  integrity "sha1-rclU6+KBw3pjGb+kAebdJIj/tw0= sha512-x/5fWmGMnbKQAaNwN+UZlV79qBLM9JFnJuJ03gIi5whrob0xV0ofNVHy9DhwGdsMJQc2OKv0oGmLzvaqvAVv+g=="

cliui@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/cliui/download/cliui-6.0.0.tgz"
  integrity "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE= sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://registry.npmmirror.com/cliui/download/cliui-7.0.4.tgz"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/clone-deep/download/clone-deep-4.0.1.tgz"
  integrity "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c= sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ=="
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/clone/download/clone-1.0.4.tgz"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/co/download/co-4.6.0.tgz"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

collect-v8-coverage@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz"
  integrity sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  resolved "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3, color-name@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/color-string/download/color-string-1.6.0.tgz"
  integrity "sha1-w5FfYf4mdnLLfh4GTJ1pIhn2wxI= sha512-c/hGS+kRWJutUBEngKKmk4iH3sD59MBkoxVapS/0wgpCz2u7XsNloxknyvBhzwEs1IbV36D9PwqLPJ2DTu3vMA=="
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.1.3:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/color/download/color-3.2.1.tgz"
  integrity sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colorette@^1.0.7:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/colorette/download/colorette-1.4.0.tgz?cache=0&sync_timestamp=1633673099786&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcolorette%2Fdownload%2Fcolorette-1.4.0.tgz"
  integrity "sha1-UZD7uHJ2JZqGrXAL/yxtb6o/ykA= sha512-Y2oEozpomLn7Q3HFP7dpww7AtMJplbM9lGZP6RDfHqmbeRjiwRg4n6VM6j4KLmRke85uWEI7JqF17f3pqdRA0g=="

colors@^1.1.2:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/colors/download/colors-1.4.0.tgz"
  integrity "sha1-xQSRR51MG9rtLJztMs98fcI2D3g= sha512-a+UqTh4kgZg/SlGvfbzDHpgRu7AAQOmmqRHJnxhRZICKFUT91brVhNNt58CMWU9PsBbv3PDCZUHbVxuDiH2mtA=="

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

command-exists@^1.2.8:
  version "1.2.9"
  resolved "https://registry.npmmirror.com/command-exists/download/command-exists-1.2.9.tgz"
  integrity sha1-xQclrzgIyKsCYP1gsB+/oluVT2k=

commander@^2.12.1, commander@^2.19.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  integrity "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM= sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="

commander@~2.13.0:
  version "2.13.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.13.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.13.0.tgz"
  integrity "sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w= sha512-MVuS359B+YzaWqjCL/c+22gfryv+mCBPHAv3zyVI2GN8EY6IRP8VwtasXn8jyyhvvq84R4ImN1OKRtcbIasjYA=="

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.1:
  version "1.7.4"
  resolved "https://registry.npmmirror.com/compression/download/compression-1.7.4.tgz"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect@^3.6.5:
  version "3.7.0"
  resolved "https://registry.npmmirror.com/connect/download/connect-3.7.0.tgz"
  integrity sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg=
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1632741882507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  integrity sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k=
  dependencies:
    safe-buffer "~5.1.1"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js-compat@^3.18.0:
  version "3.26.1"
  resolved "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.26.1.tgz"
  integrity sha512-622/KzTudvXCDLRw70iHW4KKs1aGpcRcowGWyYJr2DEBfRrd6hNJybxSWJFuZYD4ma86xhrwDDHxmDaIq4EA8A==
  dependencies:
    browserslist "^4.21.4"

core-js@^2.4.0:
  version "2.6.12"
  resolved "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.3.tgz"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^5.0.5, cosmiconfig@^5.1.0:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  integrity "sha1-BA9yaAnFked6F8CjYmykW08Wixo= sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA=="
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

cross-fetch@^3.0.4:
  version "3.1.4"
  resolved "https://registry.npmmirror.com/cross-fetch/download/cross-fetch-3.1.4.tgz"
  integrity "sha1-lyPzo6JHv4uJA586OAqSROj6Lzk= sha512-1eAtFWdIubi6T4XPy6ei9iUFoKpUkIF971QLN8lIvvvwueI65+Nw5haMNKUwfJxabqlIIDODJKGrQ66gxC0PbQ=="
  dependencies:
    node-fetch "2.6.1"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cssom@^0.4.4:
  version "0.4.4"
  resolved "https://registry.npmmirror.com/cssom/download/cssom-0.4.4.tgz"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "https://registry.npmmirror.com/cssom/download/cssom-0.3.8.tgz"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/cssstyle/download/cssstyle-2.3.0.tgz"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

data-urls@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/data-urls/download/data-urls-2.0.0.tgz"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

dayjs@^1.8.15:
  version "1.10.7"
  resolved "https://registry.npmmirror.com/dayjs/download/dayjs-1.10.7.tgz"
  integrity "sha1-LPX5Gt0oEWdIRAhmoKHSbzps5Gg= sha512-P6twpd70BcPK34K26uJ1KT3wlhpuOAPoMwJzpsIWUxHZ7wpmbdZL/hQqBDfz7hGurYSa5PhzdhDHtt319hL3ig=="

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@4, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz"
  integrity sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=
  dependencies:
    ms "2.1.2"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055713394&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.2.1:
  version "10.3.1"
  resolved "https://registry.npmmirror.com/decimal.js/download/decimal.js-10.3.1.tgz"
  integrity sha1-2MOkRKnGd0umDKatcmHDqU/V54M=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/dedent/download/dedent-0.7.0.tgz"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-is@^0.1.3, deep-is@~0.1.3:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.4.tgz"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/deepmerge/download/deepmerge-3.3.0.tgz"
  integrity "sha1-08R/1vOpPVF7FEJrBiihewEl9fc= sha512-GRQOafGHwMHpjPx9iCvTgpu9NojZ49q794EEL94JVEw6VaeA8XTUyBKvAkOOjBX9oJNiV6G3P+T+tihFjo2TqA=="

deepmerge@^4.2.2:
  version "4.2.2"
  resolved "https://registry.npmmirror.com/deepmerge/download/deepmerge-4.2.2.tgz"
  integrity sha1-RNLqNnm49NT/ujPwPYZfwee/SVU=

defaults@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/defaults/download/defaults-1.0.3.tgz"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

denodeify@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/denodeify/download/denodeify-1.2.1.tgz"
  integrity "sha1-OjYof1A05pnnV3kBBSwubJQlFjE= sha512-KNTihKNmQENUZeKu5fzfpzRqR5S2VMp4gl9RFHiWzj9DfvYQPMJ6XHKNaQxaGCXwPk6y9yme3aUoaiAe+KX+vg=="

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz?cache=0&sync_timestamp=1632469617288&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdepd%2Fdownload%2Fdepd-1.1.2.tgz"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/detect-newline/download/detect-newline-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdetect-newline%2Fdownload%2Fdetect-newline-3.1.0.tgz"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

diff-sequences@^27.0.6:
  version "27.0.6"
  resolved "https://registry.npmmirror.com/diff-sequences/download/diff-sequences-27.0.6.tgz"
  integrity "sha1-MwXLLlWgM5JAVGlcxmAZ/X+OVyM= sha512-ag6wfpBFyNXZ0p8pcuIDS//D8H062ZQJ3fzYxjpmeKjnz8W4pekL3AI8VohmyZmsWW2PWaHgjsmqR6L13101VQ=="

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/diff/download/diff-4.0.2.tgz"
  integrity "sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0= sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/dir-glob/download/dir-glob-3.0.1.tgz"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/doctrine/download/doctrine-2.1.0.tgz"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/doctrine/download/doctrine-3.0.0.tgz"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

domexception@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/domexception/download/domexception-2.0.1.tgz?cache=0&sync_timestamp=1633538712137&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdomexception%2Fdownload%2Fdomexception-2.0.1.tgz"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

electron-to-chromium@^1.4.251:
  version "1.4.284"
  resolved "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz"
  integrity sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==

emittery@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmmirror.com/emittery/download/emittery-0.8.1.tgz"
  integrity sha1-uyPMhtA7MKp1p/c0gZ3uLhunCGA=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632752198735&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz?cache=0&sync_timestamp=1632469585035&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fend-of-stream%2Fdownload%2Fend-of-stream-1.4.4.tgz"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enquirer@^2.3.5:
  version "2.3.6"
  resolved "https://registry.npmmirror.com/enquirer/download/enquirer-2.3.6.tgz"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

envinfo@^7.7.2:
  version "7.8.1"
  resolved "https://registry.npmmirror.com/envinfo/download/envinfo-7.8.1.tgz"
  integrity sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  integrity sha1-WpmnB716TFinl5AtSNgoA+3mqtg=
  dependencies:
    stackframe "^1.1.1"

errorhandler@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/errorhandler/download/errorhandler-1.5.1.tgz"
  integrity "sha1-ubpdF8+QdEzR6FE1em51v4BqmpE= sha512-rcOwbfvP1WTViVoUjcfZicVzjhjTuhSMntHh6mW3IrEiyE6mJyXvsToJUJGlGlw/2xU9P5whlWNGlIDVeCiT4A=="
  dependencies:
    accepts "~1.3.7"
    escape-html "~1.0.3"

es-abstract@^1.19.0, es-abstract@^1.19.1:
  version "1.19.1"
  resolved "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.19.1.tgz?cache=0&sync_timestamp=1633234258828&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.19.1.tgz"
  integrity sha1-1IhXlodpFpWd547aoN9FZicRXsM=
  dependencies:
    call-bind "^1.0.2"
    es-to-primitive "^1.2.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.1.1"
    get-symbol-description "^1.0.0"
    has "^1.0.3"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    is-callable "^1.2.4"
    is-negative-zero "^2.0.1"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.1"
    is-string "^1.0.7"
    is-weakref "^1.0.1"
    object-inspect "^1.11.0"
    object-keys "^1.1.1"
    object.assign "^4.1.2"
    string.prototype.trimend "^1.0.4"
    string.prototype.trimstart "^1.0.4"
    unbox-primitive "^1.0.1"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  integrity sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/escalade/download/escalade-3.1.1.tgz"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@2.0.0, escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escodegen@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/escodegen/download/escodegen-2.0.0.tgz"
  integrity sha1-XjKxKDPoqo+jXhvwvvqJOASEx90=
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-prettier@^6.10.1:
  version "6.15.0"
  resolved "https://registry.npmmirror.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-plugin-eslint-comments@^3.1.2:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz"
  integrity sha1-nhzXtEE1JquzE5MwcderoFyhL/o=
  dependencies:
    escape-string-regexp "^1.0.5"
    ignore "^5.0.5"

eslint-plugin-flowtype@2.50.3:
  version "2.50.3"
  resolved "https://registry.npmmirror.com/eslint-plugin-flowtype/download/eslint-plugin-flowtype-2.50.3.tgz?cache=0&sync_timestamp=1635741709822&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-flowtype%2Fdownload%2Feslint-plugin-flowtype-2.50.3.tgz"
  integrity sha1-YTedbc4dAQNwrNZoF0D9kT1oF18=
  dependencies:
    lodash "^4.17.10"

eslint-plugin-jest@22.4.1:
  version "22.4.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-jest/download/eslint-plugin-jest-22.4.1.tgz?cache=0&sync_timestamp=1636051559816&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-jest%2Fdownload%2Feslint-plugin-jest-22.4.1.tgz"
  integrity sha1-pf1veipBOI0W9ScHO3eAE8UYmpw=

eslint-plugin-prettier@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.1.2.tgz"
  integrity sha1-Qy5aZnZmq4TOcvlFxy932Zalybo=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.0.7:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.2.0.tgz"
  integrity sha1-jCKcJo1GiVYzTJQ7tF/IYCgPVVY=

eslint-plugin-react-native-globals@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/eslint-plugin-react-native-globals/download/eslint-plugin-react-native-globals-0.1.2.tgz"
  integrity "sha1-7hNIvCzrkSMDzmvb0i4vBF6obqI= sha512-9aEPf1JEpiTjcFAmmyw8eiIXmcNZOqaZyHO77wgm0/dWfT/oxC1SrIq8ET38pMxHYrcB6Uew+TzUVsBeczF88g=="

eslint-plugin-react-native@^3.10.0:
  version "3.11.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-react-native/download/eslint-plugin-react-native-3.11.0.tgz"
  integrity sha1-xzsIhquzl4Z+XmaJ06akGGgua6w=
  dependencies:
    "@babel/traverse" "^7.7.4"
    eslint-plugin-react-native-globals "^0.1.1"

eslint-plugin-react@^7.20.0:
  version "7.26.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-react/download/eslint-plugin-react-7.26.1.tgz"
  integrity sha1-Qbz+PjnmpawECXHBr5RDfIDapA4=
  dependencies:
    array-includes "^3.1.3"
    array.prototype.flatmap "^1.2.4"
    doctrine "^2.1.0"
    estraverse "^5.2.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.0.4"
    object.entries "^1.1.4"
    object.fromentries "^2.0.4"
    object.hasown "^1.0.0"
    object.values "^1.1.4"
    prop-types "^15.7.2"
    resolve "^2.0.0-next.3"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.5"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-6.0.0.tgz"
  integrity sha1-nPRbE8Wsjz1MUPRqUSH2Gz4xiXg=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/eslint-utils/download/eslint-utils-3.0.0.tgz?cache=0&sync_timestamp=1632470817621&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-utils%2Fdownload%2Feslint-utils-3.0.0.tgz"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  integrity "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4= sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-3.0.0.tgz"
  integrity sha1-4y6Zxs3C6wY/IE7aXbZ7/li7QYY=

eslint@^8.2.0:
  version "8.2.0"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-8.2.0.tgz?cache=0&sync_timestamp=1636158100294&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint%2Fdownload%2Feslint-8.2.0.tgz"
  integrity sha1-RNP7UG0PhmpQbZeg/A6Q7m0GqBU=
  dependencies:
    "@eslint/eslintrc" "^1.0.4"
    "@humanwhocodes/config-array" "^0.6.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^6.0.0"
    eslint-utils "^3.0.0"
    eslint-visitor-keys "^3.0.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^6.0.1"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.2.0"
    semver "^7.2.1"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^9.0.0:
  version "9.0.0"
  resolved "https://registry.npmmirror.com/espree/download/espree-9.0.0.tgz"
  integrity sha1-6QopZWmCKFAudxx6WEibGp0QcJA=
  dependencies:
    acorn "^8.5.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^3.0.0"

esprima@^4.0.0, esprima@^4.0.1, esprima@~4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/esquery/download/esquery-1.4.0.tgz"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.3.0.tgz"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

event-target-shim@^5.0.0, event-target-shim@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/event-target-shim/download/event-target-shim-5.0.1.tgz"
  integrity sha1-XU0+vflYPWOlMzzi3rdICrKwV4k=

exec-sh@^0.3.2:
  version "0.3.6"
  resolved "https://registry.npmmirror.com/exec-sh/download/exec-sh-0.3.6.tgz"
  integrity "sha1-/yZPnjJVGaYMteJzaSlDSDzKY7w= sha512-nQn+hI3yp+oD0huYhKwvYI32+JFeq+XkNcD1GAo3Y/MjxsfVGmrrzrnzjWiNY6f+pUCP440fThsFh5gZrRAU/w=="

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz"
  integrity "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg= sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA=="
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/download/execa-5.1.1.tgz"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/exit/download/exit-0.1.2.tgz"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/expect/download/expect-27.3.1.tgz?cache=0&sync_timestamp=1634626746529&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexpect%2Fdownload%2Fexpect-27.3.1.tgz"
  integrity "sha1-0PFwsfXIogCbqwvv/Uu5TwQ+OOc= sha512-MrNXV2sL9iDRebWPGOGFdPQRl2eDQNu/uhxIMShjjx74T6kC6jFIkmQ6OqXDtevjGUkyB2IT56RzDBqXf/QPCg=="
  dependencies:
    "@jest/types" "^27.2.5"
    ansi-styles "^5.0.0"
    jest-get-type "^27.3.1"
    jest-matcher-utils "^27.3.1"
    jest-message-util "^27.3.1"
    jest-regex-util "^27.0.6"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/fast-diff/download/fast-diff-1.2.0.tgz"
  integrity "sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM= sha512-xJuoT5+L99XlZ8twedaRf6Ax2TgQVxvgZOYoPKqZufmJib0tL2tegPBOZb1pVNgIhlqDlA0eO0c3wBvQcmzx4w=="

fast-glob@^3.1.1:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/fast-glob/download/fast-glob-3.2.7.tgz"
  integrity "sha1-/Wy3otfpqnp4RhEehaGW1rL3ZqE= sha512-rYGMRwip6lUMvYD3BTScMwT1HtAs2d71SMv66Vrxs0IekGZEjhM0pcMfjQPnknBt2zeCwQMEupiN02ZP4DiT1Q=="
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.npmmirror.com/fastq/download/fastq-1.13.0.tgz"
  integrity sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/fb-watchman/download/fb-watchman-2.0.1.tgz"
  integrity sha1-/IT7OdJwnPP/bXQ3BhV7tXCKioU=
  dependencies:
    bser "2.1.1"

fbjs-css-vars@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/fbjs-css-vars/download/fbjs-css-vars-1.0.2.tgz"
  integrity "sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg= sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ=="

fbjs@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/fbjs/download/fbjs-3.0.1.tgz?cache=0&sync_timestamp=1635212744753&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffbjs%2Fdownload%2Ffbjs-3.0.1.tgz"
  integrity sha1-cKBT00qWwrUTtVnq6hJNrtSazmQ=
  dependencies:
    cross-fetch "^3.0.4"
    fbjs-css-vars "^1.0.0"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.30"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/filter-obj/download/filter-obj-1.1.0.tgz"
  integrity "sha1-mzERErxsYSehbgFsbF1/GeCAXFs= sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ=="

finalhandler@1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/finalhandler/download/finalhandler-1.1.2.tgz"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-babel-config@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/find-babel-config/download/find-babel-config-1.2.0.tgz"
  integrity sha1-qbezF+tbmGDNqdVHQKjIM3oig6I=
  dependencies:
    json5 "^0.5.1"
    path-exists "^3.0.0"

find-cache-dir@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  integrity "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc= sha512-Tq6PixE0w/VMFfCgbONnkiQIVol/JJL7nRMi20fqzA4NRs9AfeqMGeRdPi3wIhYkxjeBaWh2rxwapn5Tu3IqOQ=="
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-4.1.0.tgz"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@~5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-5.0.0.tgz"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/flat-cache/download/flat-cache-3.0.4.tgz"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/flatted/download/flatted-3.2.2.tgz"
  integrity "sha1-ZL/tXLaP48p4s+shStl7Y77c5WE= sha512-JaTY/wtrcSyvXJl4IMFHPKyFur1sE9AUqc0QnhOaJ0CxHtAoIV8pYDzeEfAaNEtGkOfq4gr3LBFmdXW5mOQFnA=="

flow-bin@0.113.0:
  version "0.113.0"
  resolved "https://registry.npmmirror.com/flow-bin/download/flow-bin-0.113.0.tgz"
  integrity "sha1-ZFfSUNvG9xylHnXwCpbSPN5dmHo= sha512-76uE2LGNe50wm+Jup8Np4FBcMbyy5V2iE+K25PPIYLaEMGHrL1jnQfP9L0hTzA5oh2ZJlexRLMlaPqIYIKH9nw=="

flow-parser@0.*, flow-parser@^0.121.0:
  version "0.121.0"
  resolved "https://registry.npmmirror.com/flow-parser/download/flow-parser-0.121.0.tgz?cache=0&sync_timestamp=1635505769718&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflow-parser%2Fdownload%2Fflow-parser-0.121.0.tgz"
  integrity "sha1-n5iY6uyRqffDI+npktgatcWOYY8= sha512-1gIBiWJNR0tKUNv8gZuk7l9rVX06OuLzY9AoGio7y/JT4V1IZErEMEq2TJS+PFcw/y0RshZ1J/27VfK1UQzYVg=="

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

form-data@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/form-data/download/form-data-3.0.1.tgz"
  integrity sha1-69U3kbeDVqma+aMA1CgsTV65dV8=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-extra@9.0.0:
  version "9.0.0"
  resolved "https://registry.npmmirror.com/fs-extra/download/fs-extra-9.0.0.tgz"
  integrity "sha1-tq/DEDbiR7JGbcmcKa55fV1FgKM= sha512-pmEYSk3vYsG/bF651KPUXZ+hvjpgWYw/Gc7W9NFUe3ZVLczKKWIij3IKpOrQcdw4TILtibFslZ0UmR8Vvzig4g=="
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^1.0.0"

fs-extra@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs-extra/download/fs-extra-1.0.0.tgz"
  integrity "sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA= sha512-VerQV6vEKuhDWD2HGOybV6v5I73syoc/cXAbKlgTC7M/oFVEtklWlp9QH2Ijw3IaWDOQcMkldSPa7zXy79Z/UQ=="
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^2.1.0"
    klaw "^1.0.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmmirror.com/fs-extra/download/fs-extra-8.1.0.tgz"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.1.2, fsevents@^2.3.2:
  version "2.3.3"
  resolved "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmmirror.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.1, get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.0.2, get-intrinsic@^1.1.0, get-intrinsic@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/get-package-type/download/get-package-type-0.1.0.tgz"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/get-stdin/download/get-stdin-6.0.0.tgz"
  integrity "sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs= sha512-jp4tHawyV7+fkkSKyvjuLZswblUtz+SQKzSWnBbii16BuZksJlU1wuBYXY75r+duh/llF1ur6oNwi+2ZzjKZ7g=="

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/get-stream/download/get-stream-6.0.1.tgz"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz"
  integrity sha1-f9uByQAQH71WTdXxowr1qtweWNY=
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getenv@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/getenv/download/getenv-1.0.0.tgz"
  integrity "sha1-h08udUT7ylPHpHOPN96GBcP8/DE= sha512-7yetJWqbS9sbn0vIfliPsFgoXMKn/YMF+Wuiog97x+urnSRRRZ7xB+uVkwGKzRgq9CDFfMQnE9ruL5DHv9c6Xg=="

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953971963&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-6.0.2.tgz?cache=0&sync_timestamp=1632953971963&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-6.0.2.tgz"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@7.1.6:
  version "7.1.6"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.1.6.tgz"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.1.1, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.1.6:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz"
  integrity sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.6.0, globals@^13.9.0:
  version "13.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz"
  integrity sha1-TXM3YDBCMKAILtluIeXFZfiYCJ4=
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.3:
  version "11.0.4"
  resolved "https://registry.npmmirror.com/globby/download/globby-11.0.4.tgz"
  integrity "sha1-LLr/d8Lypi5x6bKBOme5ejowAaU= sha512-9O4MVG9ioZJ08ffbcyVYyLOJLk5JQ688pJ4eMGLpdWLHq/Wr1D9BlriLQyL0E+jbkuePVZXYFj47QM/v093wHg=="
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.2, graceful-fs@^4.1.3, graceful-fs@^4.1.6, graceful-fs@^4.1.9, graceful-fs@^4.2.0, graceful-fs@^4.2.4:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz"
  integrity sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=

has-bigints@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/has-bigints/download/has-bigints-1.0.1.tgz"
  integrity sha1-ZP5qywIGc+O3jbA1pa9pqp0HsRM=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz"
  integrity sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has/download/has-1.0.3.tgz"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hermes-engine@~0.9.0:
  version "0.9.0"
  resolved "https://registry.npmmirror.com/hermes-engine/download/hermes-engine-0.9.0.tgz"
  integrity sha1-hNnP6E6PaxsgINbnGzUM7ITtmC8=

hermes-parser@0.4.7:
  version "0.4.7"
  resolved "https://registry.npmmirror.com/hermes-parser/download/hermes-parser-0.4.7.tgz"
  integrity "sha1-QQ9RKdVxg3hNIFoFOOb73PYUyeo= sha512-jc+zCtXbtwTiXoMAoXOHepxAaGVFIp89wwE9qcdwnMd/uGVEtPoY8FaFSsx0ThPvyKirdR2EsIIDVrpbSXz1Ag=="

hermes-profile-transformer@^0.0.6:
  version "0.0.6"
  resolved "https://registry.npmmirror.com/hermes-profile-transformer/download/hermes-profile-transformer-0.0.6.tgz"
  integrity "sha1-vQ9ezO2oDdDdquRDRpqyb7OPwns= sha512-cnN7bQUm65UWOy6cbGcCcZ3rpwW8Q/j4OP5aWRhEry4Z2t2aR1cjrbp0BS+KiBN0smvP1caBgAuxutvyvJILzQ=="
  dependencies:
    source-map "^0.7.3"

hoist-non-react-statics@^2.3.1:
  version "2.5.5"
  resolved "https://registry.npmmirror.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz"
  integrity "sha1-xZA89AnA39kI84jmGdhrnBF0y0c= sha512-rqcy4pJo55FTTLWt+bU8ukscqHeE/e9KWvsOW2b/a3afxQZhwkQdT1rPPCJ0rYXdj4vNcasY8zHTH+jF/qStxw=="

hoist-non-react-statics@^3.3.0, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmmirror.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/html-escaper/download/html-escaper-2.0.2.tgz"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/https-proxy-agent/download/https-proxy-agent-5.0.0.tgz"
  integrity sha1-4qkFQqu2inYuCghQ9sntrf2FBrI=
  dependencies:
    agent-base "6"
    debug "4"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/human-signals/download/human-signals-2.1.0.tgz"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.0.5, ignore@^5.1.4, ignore@^5.1.8:
  version "5.1.9"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz"
  integrity "sha1-nsGly+jhRG7GDUQgBg1Dqm5zgvs= sha512-2zeMQpbKz5dhZ9IwL0gbxSW5w0NK/MSAMtNuhgIHEPmaU3vPdKPL0UdvUCXs5SS4JAwsBxysK5sFMW8ocFiVjQ=="

image-size@^0.6.0:
  version "0.6.3"
  resolved "https://registry.npmmirror.com/image-size/download/image-size-0.6.3.tgz"
  integrity "sha1-5+XGW7U0vXzc7dbLUWYnKoX3X7I= sha512-47xSUiQioGaB96nqtp5/q55m0aBQSQdyIloMOc/x+QVTDZLNmXE892IIDrJ0hM1A5vcNUDD5tDffkSP5lCaIIA=="

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/import-fresh/download/import-fresh-2.0.0.tgz"
  integrity "sha1-2BNVwVYS04bGH53dOSLUMEgipUY= sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg=="
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/import-fresh/download/import-fresh-3.3.0.tgz"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/import-local/download/import-local-3.0.3.tgz"
  integrity sha1-TVHCxJXKk5PaJZ7Ga2LgIpICEeA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

internal-slot@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/internal-slot/download/internal-slot-1.0.3.tgz"
  integrity sha1-c0fjB97uovqsKsYgXUvH00ln9Zw=
  dependencies:
    get-intrinsic "^1.1.0"
    has "^1.0.3"
    side-channel "^1.0.4"

invariant@2.2.4, invariant@^2.2.4:
  version "2.2.4"
  resolved "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/is-bigint/download/is-bigint-1.0.4.tgz"
  integrity sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM=
  dependencies:
    has-bigints "^1.0.1"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz"
  integrity sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4, is-callable@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/is-callable/download/is-callable-1.2.4.tgz"
  integrity sha1-RzAdWN0CWUB4ZVR4U99tYf5HGUU=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1635261090481&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.2.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz?cache=0&sync_timestamp=1634236434261&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.8.0.tgz"
  integrity "sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug= sha512-vd15qHsaqrRL7dtH6QNuy0ndJmRDrS9HAM1CAiSifNUFv4x1a0CCVsj18hJ1mShxIG6T2i1sO78MkP56r0nYRw=="
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.5.tgz"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "https://registry.npmmirror.com/is-directory/download/is-directory-0.3.1.tgz"
  integrity "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE= sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw=="

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934573225&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-negative-zero@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-negative-zero/download/is-negative-zero-2.0.1.tgz"
  integrity sha1-PedGwY3aIxkkGlNnWQjY92bxHCQ=

is-number-object@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/is-number-object/download/is-number-object-1.0.6.tgz"
  integrity sha1-anqvg4x/BoalC0VT9+VKlklOifA=
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-plain-obj@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz"
  integrity "sha1-ReQuN/zPH0Dajl927iFRWEDAkoc= sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA=="

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/is-regex/download/is-regex-1.1.4.tgz"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-shared-array-buffer@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.1.tgz?cache=0&sync_timestamp=1633062271086&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-shared-array-buffer%2Fdownload%2Fis-shared-array-buffer-1.0.1.tgz"
  integrity sha1-l7DIX72stZycRG/mU7gs8rW3z+Y=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-stream/download/is-stream-2.0.1.tgz"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/is-string/download/is-string-1.0.7.tgz"
  integrity sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.4.tgz"
  integrity sha1-ptrJO2NbBjymhyI23oiRClevE5w=
  dependencies:
    has-symbols "^1.0.2"

is-typedarray@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-weakref@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/is-weakref/download/is-weakref-1.0.1.tgz"
  integrity sha1-hC26TsF/qayYUN8tbvvBc3J08qI=
  dependencies:
    call-bind "^1.0.0"

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-wsl/download/is-wsl-1.1.0.tgz"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz?cache=0&sync_timestamp=1634527381492&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fistanbul-lib-coverage%2Fdownload%2Fistanbul-lib-coverage-3.2.0.tgz"
  integrity sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM=

istanbul-lib-instrument@^4.0.3:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz"
  integrity "sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0= sha512-BXgQl9kf4WTCPCCpmFGoJkz/+uhvm7h7PFKUYxh7qarQd3ER33vHG//qaE8eN25l07YqZPpHXU9I09l/RD5aGQ=="
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-instrument@^5.0.4:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-5.1.0.tgz"
  integrity sha1-e0kZi2V7J6cwuOnLYB8eG/8kxZo=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.2:
  version "3.0.5"
  resolved "https://registry.npmmirror.com/istanbul-reports/download/istanbul-reports-3.0.5.tgz"
  integrity "sha1-olgBB+cSeeptZh3e3pKf/G1pM4Q= sha512-5+19PlhnGabNWB7kOFnuxT8H3T/iIyQzIbQMxXsURmmvKg86P2sbkrGOT77VnHw0Qr0gc2XzRaRfMZYYbSQCJQ=="
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jest-changed-files@^27.3.0:
  version "27.3.0"
  resolved "https://registry.npmmirror.com/jest-changed-files/download/jest-changed-files-27.3.0.tgz"
  integrity "sha1-IqAswrNFg/xm5EMXHcJxwFKdJjw= sha512-9DJs9garMHv4RhylUMZgbdCJ3+jHSkpL9aaVKp13xtXAD80qLTLrqcDZL1PHA9dYA0bCI86Nv2BhkLpLhrBcPg=="
  dependencies:
    "@jest/types" "^27.2.5"
    execa "^5.0.0"
    throat "^6.0.1"

jest-circus@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-circus/download/jest-circus-27.3.1.tgz?cache=0&sync_timestamp=1634627345495&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-circus%2Fdownload%2Fjest-circus-27.3.1.tgz"
  integrity "sha1-FnnnQ4fLvwxqi0LeljJQpkaeB5c= sha512-v1dsM9II6gvXokgqq6Yh2jHCpfg7ZqV4jWY66u7npz24JnhP3NHxI0sKT7+ZMQ7IrOWHYAaeEllOySbDbWsiXw=="
  dependencies:
    "@jest/environment" "^27.3.1"
    "@jest/test-result" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^0.7.0"
    expect "^27.3.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.3.1"
    jest-matcher-utils "^27.3.1"
    jest-message-util "^27.3.1"
    jest-runtime "^27.3.1"
    jest-snapshot "^27.3.1"
    jest-util "^27.3.1"
    pretty-format "^27.3.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"
    throat "^6.0.1"

jest-cli@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-cli/download/jest-cli-27.3.1.tgz?cache=0&sync_timestamp=1634626754387&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-cli%2Fdownload%2Fjest-cli-27.3.1.tgz"
  integrity sha1-tXb50Ua6ZkPOChYteCtAFStrHRY=
  dependencies:
    "@jest/core" "^27.3.1"
    "@jest/test-result" "^27.3.1"
    "@jest/types" "^27.2.5"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    import-local "^3.0.2"
    jest-config "^27.3.1"
    jest-util "^27.3.1"
    jest-validate "^27.3.1"
    prompts "^2.0.1"
    yargs "^16.2.0"

jest-config@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-config/download/jest-config-27.3.1.tgz?cache=0&sync_timestamp=1634626716976&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-config%2Fdownload%2Fjest-config-27.3.1.tgz"
  integrity "sha1-yzt/aqqMCn2q1PK5VziZyn4Ju60= sha512-KY8xOIbIACZ/vdYCKSopL44I0xboxC751IX+DXL2+Wx6DKNycyEfV3rryC3BPm5Uq/BBqDoMrKuqLEUNJmMKKg=="
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^27.3.1"
    "@jest/types" "^27.2.5"
    babel-jest "^27.3.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.1"
    graceful-fs "^4.2.4"
    jest-circus "^27.3.1"
    jest-environment-jsdom "^27.3.1"
    jest-environment-node "^27.3.1"
    jest-get-type "^27.3.1"
    jest-jasmine2 "^27.3.1"
    jest-regex-util "^27.0.6"
    jest-resolve "^27.3.1"
    jest-runner "^27.3.1"
    jest-util "^27.3.1"
    jest-validate "^27.3.1"
    micromatch "^4.0.4"
    pretty-format "^27.3.1"

jest-diff@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-diff/download/jest-diff-27.3.1.tgz?cache=0&sync_timestamp=1634626740654&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-diff%2Fdownload%2Fjest-diff-27.3.1.tgz"
  integrity "sha1-0ndf6hVBH19a7aKl4CwvNkQPbVU= sha512-PCeuAH4AWUo2O5+ksW4pL9v5xJAcIKPUPfIhZBcG1RKv/0+dvaWTQK1Nrau8d67dp65fOqbeMdoil+6PedyEPQ=="
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^27.0.6"
    jest-get-type "^27.3.1"
    pretty-format "^27.3.1"

jest-docblock@^27.0.6:
  version "27.0.6"
  resolved "https://registry.npmmirror.com/jest-docblock/download/jest-docblock-27.0.6.tgz"
  integrity "sha1-zHgmas9/5pPKRiy72g6k5jnk5fM= sha512-Fid6dPcjwepTFraz0YxIMCi7dejjJ/KL9FBjPYhBp4Sv1Y9PdhImlKZqYU555BlN4TQKaTc+F2Av1z+anVyGkA=="
  dependencies:
    detect-newline "^3.0.0"

jest-each@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-each/download/jest-each-27.3.1.tgz?cache=0&sync_timestamp=1634626724382&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-each%2Fdownload%2Fjest-each-27.3.1.tgz"
  integrity "sha1-FMVrtPGN0Y3GvdhTkZtfFqF3Yf8= sha512-E4SwfzKJWYcvOYCjOxhZcxwL+AY0uFMvdCOwvzgutJiaiodFjkxQQDxHm8FQBeTqDnSmKsQWn7ldMRzTn2zJaQ=="
  dependencies:
    "@jest/types" "^27.2.5"
    chalk "^4.0.0"
    jest-get-type "^27.3.1"
    jest-util "^27.3.1"
    pretty-format "^27.3.1"

jest-environment-jsdom@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-environment-jsdom/download/jest-environment-jsdom-27.3.1.tgz?cache=0&sync_timestamp=1634626714740&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-environment-jsdom%2Fdownload%2Fjest-environment-jsdom-27.3.1.tgz"
  integrity "sha1-Y6w21o96kwNJTfeDSUhWIitX9z4= sha512-3MOy8qMzIkQlfb3W1TfrD7uZHj+xx8Olix5vMENkj5djPmRqndMaXtpnaZkxmxM+Qc3lo+yVzJjzuXbCcZjAlg=="
  dependencies:
    "@jest/environment" "^27.3.1"
    "@jest/fake-timers" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    jest-mock "^27.3.0"
    jest-util "^27.3.1"
    jsdom "^16.6.0"

jest-environment-node@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-environment-node/download/jest-environment-node-27.3.1.tgz?cache=0&sync_timestamp=1634626741234&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-27.3.1.tgz"
  integrity "sha1-r30O7QTtr7dAMRswPz/nyMJwFLs= sha512-T89F/FgkE8waqrTSA7/ydMkcc52uYPgZZ6q8OaZgyiZkJb5QNNCF6oPZjH9IfPFfcc9uBWh1574N0kY0pSvTXw=="
  dependencies:
    "@jest/environment" "^27.3.1"
    "@jest/fake-timers" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    jest-mock "^27.3.0"
    jest-util "^27.3.1"

jest-get-type@^26.3.0:
  version "26.3.0"
  resolved "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-26.3.0.tgz?cache=0&sync_timestamp=1634626733231&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-get-type%2Fdownload%2Fjest-get-type-26.3.0.tgz"
  integrity "sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA= sha512-TpfaviN1R2pQWkIihlfEanwOXK0zcxrKEE4MlU6Tn7keoXdN6/3gK/xl0yEh8DOunn5pOVGKf8hB4R9gVh04ig=="

jest-get-type@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-27.3.1.tgz?cache=0&sync_timestamp=1634626733231&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-get-type%2Fdownload%2Fjest-get-type-27.3.1.tgz"
  integrity "sha1-qKKwoStQFpdzCZ7uYKDm3RFCPv8= sha512-+Ilqi8hgHSAdhlQ3s12CAVNd8H96ZkQBfYoXmArzZnOfAtVAJEiPDBirjByEblvG/4LPJmkL+nBqPO3A1YJAEg=="

jest-haste-map@^26.5.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz?cache=0&sync_timestamp=1634626734781&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-haste-map%2Fdownload%2Fjest-haste-map-26.6.2.tgz"
  integrity "sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo= sha512-easWIJXIw71B2RdR8kgqpjQrbMRWQBgiBwXYEhtGUTaX+doCjBheluShdDMeR8IMfJiTqH4+zfhtg29apJf/8w=="
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^26.0.0"
    jest-serializer "^26.6.2"
    jest-util "^26.6.2"
    jest-worker "^26.6.2"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-haste-map@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-27.3.1.tgz?cache=0&sync_timestamp=1634626734781&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-haste-map%2Fdownload%2Fjest-haste-map-27.3.1.tgz"
  integrity "sha1-dlb71kv0i9qQTnWfydk+LIBzU+4= sha512-lYfNZIzwPccDJZIyk9Iz5iQMM/MH56NIIcGj7AFU1YyA4ewWFBl8z+YPJuSCRML/ee2cCt2y3W4K3VXPT6Nhzg=="
  dependencies:
    "@jest/types" "^27.2.5"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.4"
    jest-regex-util "^27.0.6"
    jest-serializer "^27.0.6"
    jest-util "^27.3.1"
    jest-worker "^27.3.1"
    micromatch "^4.0.4"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.3.2"

jest-jasmine2@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-jasmine2/download/jest-jasmine2-27.3.1.tgz?cache=0&sync_timestamp=1634626719825&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-27.3.1.tgz"
  integrity "sha1-3209B8fa/DRP60OgBypvCUWNMrA= sha512-WK11ZUetDQaC09w4/j7o4FZDUIp+4iYWH/Lik34Pv7ukL+DuXFGdnmmi7dT58J2ZYKFB5r13GyE0z3NPeyJmsg=="
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^27.3.1"
    "@jest/source-map" "^27.0.6"
    "@jest/test-result" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    expect "^27.3.1"
    is-generator-fn "^2.0.0"
    jest-each "^27.3.1"
    jest-matcher-utils "^27.3.1"
    jest-message-util "^27.3.1"
    jest-runtime "^27.3.1"
    jest-snapshot "^27.3.1"
    jest-util "^27.3.1"
    pretty-format "^27.3.1"
    throat "^6.0.1"

jest-leak-detector@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-leak-detector/download/jest-leak-detector-27.3.1.tgz?cache=0&sync_timestamp=1634626738473&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-leak-detector%2Fdownload%2Fjest-leak-detector-27.3.1.tgz"
  integrity "sha1-f7Yywpku9weh5zKG4ecE+cwXcrI= sha512-78QstU9tXbaHzwlRlKmTpjP9k4Pvre5l0r8Spo4SbFFVy/4Abg9I6ZjHwjg2QyKEAMg020XcjP+UgLZIY50yEg=="
  dependencies:
    jest-get-type "^27.3.1"
    pretty-format "^27.3.1"

jest-matcher-utils@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-matcher-utils/download/jest-matcher-utils-27.3.1.tgz?cache=0&sync_timestamp=1634626742177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-matcher-utils%2Fdownload%2Fjest-matcher-utils-27.3.1.tgz"
  integrity "sha1-JXrWHlSm1AROCA2F29xKCIEenBw= sha512-hX8N7zXS4k+8bC1Aj0OWpGb7D3gIXxYvPNK1inP5xvE4ztbz3rc4AkI6jGVaerepBnfWB17FL5lWFJT3s7qo8w=="
  dependencies:
    chalk "^4.0.0"
    jest-diff "^27.3.1"
    jest-get-type "^27.3.1"
    pretty-format "^27.3.1"

jest-message-util@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-message-util/download/jest-message-util-27.3.1.tgz?cache=0&sync_timestamp=1634626735326&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-message-util%2Fdownload%2Fjest-message-util-27.3.1.tgz"
  integrity "sha1-98JWiK00EKsQvLhivP4xUjRcZDY= sha512-bh3JEmxsTZ/9rTm0jQrPElbY2+y48Rw2t47uMfByNyUVR+OfPh4anuyKsGqsNkXk/TI4JbLRZx+7p7Hdt6q1yg=="
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^27.2.5"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    micromatch "^4.0.4"
    pretty-format "^27.3.1"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^27.3.0:
  version "27.3.0"
  resolved "https://registry.npmmirror.com/jest-mock/download/jest-mock-27.3.0.tgz"
  integrity "sha1-3fDsPMPmjIzNSJvvTR9SVXGhuGc= sha512-ziZiLk0elZOQjD08bLkegBzv5hCABu/c8Ytx45nJKkysQwGaonvmTxwjLqEA4qGdasq9o2I8/HtdGMNnVsMTGw=="
  dependencies:
    "@jest/types" "^27.2.5"
    "@types/node" "*"

jest-pnp-resolver@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz"
  integrity sha1-twSsCuAoqJEIpNBAs/kZ393I4zw=

jest-regex-util@^26.0.0:
  version "26.0.0"
  resolved "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz"
  integrity "sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig= sha512-Gv3ZIs/nA48/Zvjrl34bf+oD76JHiGDUxNOVgUjh3j890sblXryjY4rss71fPtD/njchl6PSE2hIhvyWa1eT0A=="

jest-regex-util@^27.0.6:
  version "27.0.6"
  resolved "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-27.0.6.tgz"
  integrity "sha1-AuESCCk1rpSc5dE7JnXbPYyH2cU= sha512-SUhPzBsGa1IKm8hx2F4NfTGGp+r7BXJ4CulsZ1k2kI+mGLG+lxGrs76veN2LF/aUdGosJBzKgXmNCw+BzFqBDQ=="

jest-resolve-dependencies@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-resolve-dependencies/download/jest-resolve-dependencies-27.3.1.tgz?cache=0&sync_timestamp=1634626724098&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-resolve-dependencies%2Fdownload%2Fjest-resolve-dependencies-27.3.1.tgz"
  integrity "sha1-hbmb29+kbiyBxiKPxMkQdvYk9uI= sha512-X7iLzY8pCiYOnvYo2YrK3P9oSE8/3N2f4pUZMJ8IUcZnT81vlSonya1KTO9ZfKGuC+svE6FHK/XOb8SsoRUV1A=="
  dependencies:
    "@jest/types" "^27.2.5"
    jest-regex-util "^27.0.6"
    jest-snapshot "^27.3.1"

jest-resolve@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-resolve/download/jest-resolve-27.3.1.tgz?cache=0&sync_timestamp=1634626716645&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-resolve%2Fdownload%2Fjest-resolve-27.3.1.tgz"
  integrity "sha1-DlVCFyoaoCcL5vZqZYiGR73XSj4= sha512-Dfzt25CFSPo3Y3GCbxynRBZzxq9AdyNN+x/v2IqYx6KVT5Z6me2Z/PsSGFSv3cOSUZqJ9pHxilao/I/m9FouLw=="
  dependencies:
    "@jest/types" "^27.2.5"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    jest-haste-map "^27.3.1"
    jest-pnp-resolver "^1.2.2"
    jest-util "^27.3.1"
    jest-validate "^27.3.1"
    resolve "^1.20.0"
    resolve.exports "^1.1.0"
    slash "^3.0.0"

jest-runner@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-runner/download/jest-runner-27.3.1.tgz?cache=0&sync_timestamp=1634626715302&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-runner%2Fdownload%2Fjest-runner-27.3.1.tgz"
  integrity "sha1-HVlNy/O9hgCn6DnnkDhFWer5bj4= sha512-r4W6kBn6sPr3TBwQNmqE94mPlYVn7fLBseeJfo4E2uCTmAyDFm2O5DYAQAFP7Q3YfiA/bMwg8TVsciP7k0xOww=="
  dependencies:
    "@jest/console" "^27.3.1"
    "@jest/environment" "^27.3.1"
    "@jest/test-result" "^27.3.1"
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.8.1"
    exit "^0.1.2"
    graceful-fs "^4.2.4"
    jest-docblock "^27.0.6"
    jest-environment-jsdom "^27.3.1"
    jest-environment-node "^27.3.1"
    jest-haste-map "^27.3.1"
    jest-leak-detector "^27.3.1"
    jest-message-util "^27.3.1"
    jest-resolve "^27.3.1"
    jest-runtime "^27.3.1"
    jest-util "^27.3.1"
    jest-worker "^27.3.1"
    source-map-support "^0.5.6"
    throat "^6.0.1"

jest-runtime@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-runtime/download/jest-runtime-27.3.1.tgz?cache=0&sync_timestamp=1634626721625&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-runtime%2Fdownload%2Fjest-runtime-27.3.1.tgz"
  integrity "sha1-gPoy64X+WvV1hl3fN5h0d37pk9c= sha512-qtO6VxPbS8umqhEDpjA4pqTkKQ1Hy4ZSi9mDVeE9Za7LKBo2LdW2jmT+Iod3XFaJqINikZQsn2wEi0j9wPRbLg=="
  dependencies:
    "@jest/console" "^27.3.1"
    "@jest/environment" "^27.3.1"
    "@jest/globals" "^27.3.1"
    "@jest/source-map" "^27.0.6"
    "@jest/test-result" "^27.3.1"
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    execa "^5.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.4"
    jest-haste-map "^27.3.1"
    jest-message-util "^27.3.1"
    jest-mock "^27.3.0"
    jest-regex-util "^27.0.6"
    jest-resolve "^27.3.1"
    jest-snapshot "^27.3.1"
    jest-util "^27.3.1"
    jest-validate "^27.3.1"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^16.2.0"

jest-serializer@^26.6.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-26.6.2.tgz"
  integrity "sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE= sha512-S5wqyz0DXnNJPd/xfIzZ5Xnp1HrJWBczg8mMfMpN78OJ5eDxXyf+Ygld9wX1DnUWbIbhM1YDY95NjR4CBXkb2g=="
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-serializer@^27.0.6:
  version "27.0.6"
  resolved "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-27.0.6.tgz"
  integrity "sha1-k6bHTgEyuBotVGIyUcRsSYu1vsE= sha512-PtGdVK9EGC7dsaziskfqaAPib6wTViY3G8E5wz9tLVPhHyiDNTZn/xjZ4khAw+09QkoOVpn7vF5nPSN6dtBexA=="
  dependencies:
    "@types/node" "*"
    graceful-fs "^4.2.4"

jest-snapshot@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-snapshot/download/jest-snapshot-27.3.1.tgz?cache=0&sync_timestamp=1634626720436&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-snapshot%2Fdownload%2Fjest-snapshot-27.3.1.tgz"
  integrity "sha1-HaXAcSolLXCRfUbANwVPWRjEnuQ= sha512-APZyBvSgQgOT0XumwfFu7X3G5elj6TGhCBLbBdn3R1IzYustPGPE38F51dBWMQ8hRXa9je0vAdeVDtqHLvB6lg=="
  dependencies:
    "@babel/core" "^7.7.2"
    "@babel/generator" "^7.7.2"
    "@babel/parser" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.0.0"
    "@jest/transform" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.1.5"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^27.3.1"
    graceful-fs "^4.2.4"
    jest-diff "^27.3.1"
    jest-get-type "^27.3.1"
    jest-haste-map "^27.3.1"
    jest-matcher-utils "^27.3.1"
    jest-message-util "^27.3.1"
    jest-resolve "^27.3.1"
    jest-util "^27.3.1"
    natural-compare "^1.4.0"
    pretty-format "^27.3.1"
    semver "^7.3.2"

jest-util@^26.6.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/jest-util/download/jest-util-26.6.2.tgz?cache=0&sync_timestamp=1634626734205&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-util%2Fdownload%2Fjest-util-26.6.2.tgz"
  integrity "sha1-kHU12+TVpstMR6ybkm9q8pV2y8E= sha512-MDW0fKfsn0OI7MS7Euz6h8HNDXVQ0gaM9uW6RjfDmd1DAFcaxX9OqIakHIqhbnmF08Cf2DLDG+ulq8YQQ0Lp0Q=="
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    chalk "^4.0.0"
    graceful-fs "^4.2.4"
    is-ci "^2.0.0"
    micromatch "^4.0.2"

jest-util@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-util/download/jest-util-27.3.1.tgz?cache=0&sync_timestamp=1634626734205&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-util%2Fdownload%2Fjest-util-27.3.1.tgz"
  integrity "sha1-pYzce2yKVgyqye1r38Tk/yP4BCk= sha512-8fg+ifEH3GDryLQf/eKZck1DEs2YuVPBCMOaHQxVVLmQwl/CDhWzrvChTX4efLZxGrw+AA0mSXv78cyytBt/uw=="
  dependencies:
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.4"
    picomatch "^2.2.3"

jest-validate@^26.5.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/jest-validate/download/jest-validate-26.6.2.tgz?cache=0&sync_timestamp=1634626742917&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-validate%2Fdownload%2Fjest-validate-26.6.2.tgz"
  integrity "sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw= sha512-NEYZ9Aeyj0i5rQqbq+tpIOom0YS1u2MVu6+euBsvpgIme+FOfRmoC4R5p0JiAUpaFvFy24xgrpMknarR/93XjQ=="
  dependencies:
    "@jest/types" "^26.6.2"
    camelcase "^6.0.0"
    chalk "^4.0.0"
    jest-get-type "^26.3.0"
    leven "^3.1.0"
    pretty-format "^26.6.2"

jest-validate@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-validate/download/jest-validate-27.3.1.tgz"
  integrity "sha1-OjldYaGc0TrpBUr4za8pkRbviiQ= sha512-3H0XCHDFLA9uDII67Bwi1Vy7AqwA5HqEEjyy934lgVhtJ3eisw6ShOF1MDmRPspyikef5MyExvIm0/TuLzZ86Q=="
  dependencies:
    "@jest/types" "^27.2.5"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^27.3.1"
    leven "^3.1.0"
    pretty-format "^27.3.1"

jest-watcher@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-watcher/download/jest-watcher-27.3.1.tgz?cache=0&sync_timestamp=1634626751356&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-watcher%2Fdownload%2Fjest-watcher-27.3.1.tgz"
  integrity "sha1-ul4LxqqENhK1Tdt/AJ0cv/fgXz4= sha512-9/xbV6chABsGHWh9yPaAGYVVKurWoP3ZMCv6h+O1v9/+pkOroigs6WzZ0e9gLP/njokUwM7yQhr01LKJVMkaZA=="
  dependencies:
    "@jest/test-result" "^27.3.1"
    "@jest/types" "^27.2.5"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    jest-util "^27.3.1"
    string-length "^4.0.1"

jest-worker@^26.0.0, jest-worker@^26.6.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/jest-worker/download/jest-worker-26.6.2.tgz"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-worker/download/jest-worker-27.3.1.tgz?cache=0&sync_timestamp=1634626737887&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-worker%2Fdownload%2Fjest-worker-27.3.1.tgz"
  integrity "sha1-De9/6uW4BCvjhHl5mut7X6ysJLI= sha512-ks3WCzsiZaOPJl/oMsDjaf0TRiSv7ctNgs0FqRr2nARsovz6AWWy4oLElwcquGSz692DzgZQrCLScPNs5YlC4g=="
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest/download/jest-27.3.1.tgz?cache=0&sync_timestamp=1634626752478&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest%2Fdownload%2Fjest-27.3.1.tgz"
  integrity sha1-tbq2To9WtvfidboYNomLDZ8eXIo=
  dependencies:
    "@jest/core" "^27.3.1"
    import-local "^3.0.2"
    jest-cli "^27.3.1"

jetifier@^1.6.2:
  version "1.6.8"
  resolved "https://registry.npmmirror.com/jetifier/download/jetifier-1.6.8.tgz"
  integrity "sha1-6IBoaXh1y9qYwyRykCxNN1Ykd5g= sha512-3Zi16h6L5tXDRQJTb221cnRoVG9/9OvreLdLU2/ZjRv/GILL+2Cemt0IKvkowwkDpvouAU1DQPOJ7qaiHeIdrw=="

joi@^17.2.1:
  version "17.4.2"
  resolved "https://registry.npmmirror.com/joi/download/joi-17.4.2.tgz"
  integrity "sha1-AvTrXPiOUV5hSDAjk3ncu+KM5/c= sha512-Lm56PP+n0+Z2A2rfRvsfWVDXGEWjXxatPopkQ8qQ5mxCEhwHG+Ettgg5o98FFaxilOxozoa14cFhrE/hOzh/Nw=="
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.0"
    "@sideway/formula" "^3.0.0"
    "@sideway/pinpoint" "^2.0.0"

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz"
  integrity "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc= sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g=="
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/js-yaml/download/js-yaml-4.1.0.tgz"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsc-android@^250230.2.1:
  version "250230.2.1"
  resolved "https://registry.npmmirror.com/jsc-android/download/jsc-android-250230.2.1.tgz"
  integrity "sha1-N5AxOpcFhqA6sK1H3vvITfVPG4M= sha512-KmxeBlRjwoqCnBBKGsihFtvsBHyUFlBxJPK4FzeYcIuBfdjv6jFys44JITAgSTbQD+vIdwMEfyZklsuQX0yI1Q=="

jscodeshift@^0.11.0:
  version "0.11.0"
  resolved "https://registry.npmmirror.com/jscodeshift/download/jscodeshift-0.11.0.tgz"
  integrity "sha1-T5UDlAjz8GsOObtNU7wxOfUzDi8= sha512-SdRK2C7jjs4k/kT2mwtO07KJN9RnjxtKn03d9JVj6c3j9WwaLcFYsICYDnLAzY0hp+wG2nxl+Cm2jWLiNVYb8g=="
  dependencies:
    "@babel/core" "^7.1.6"
    "@babel/parser" "^7.1.6"
    "@babel/plugin-proposal-class-properties" "^7.1.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.1.0"
    "@babel/plugin-proposal-optional-chaining" "^7.1.0"
    "@babel/plugin-transform-modules-commonjs" "^7.1.0"
    "@babel/preset-flow" "^7.0.0"
    "@babel/preset-typescript" "^7.1.0"
    "@babel/register" "^7.0.0"
    babel-core "^7.0.0-bridge.0"
    colors "^1.1.2"
    flow-parser "0.*"
    graceful-fs "^4.2.4"
    micromatch "^3.1.10"
    neo-async "^2.5.0"
    node-dir "^0.1.17"
    recast "^0.20.3"
    temp "^0.8.1"
    write-file-atomic "^2.3.0"

jsdom@^16.6.0:
  version "16.7.0"
  resolved "https://registry.npmmirror.com/jsdom/download/jsdom-16.7.0.tgz?cache=0&sync_timestamp=1635866517769&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjsdom%2Fdownload%2Fjsdom-16.7.0.tgz"
  integrity "sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA= sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw=="
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^0.5.1:
  version "0.5.1"
  resolved "https://registry.npmmirror.com/json5/download/json5-0.5.1.tgz"
  integrity sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=

json5@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/json5/download/json5-2.2.0.tgz"
  integrity sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM=
  dependencies:
    minimist "^1.2.5"

jsonfile@^2.1.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/jsonfile/download/jsonfile-2.4.0.tgz"
  integrity sha1-NzaitCi4e72gzIO1P6PWM6NcKug=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/jsonfile/download/jsonfile-6.1.0.tgz"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.npmmirror.com/jsonify/download/jsonify-0.0.0.tgz"
  integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.2.1"
  resolved "https://registry.npmmirror.com/jsx-ast-utils/download/jsx-ast-utils-3.2.1.tgz"
  integrity sha1-cguXv+fZAbkn2Hw3c2N66OpIeBs=
  dependencies:
    array-includes "^3.1.3"
    object.assign "^4.1.2"

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klaw@^1.0.0:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/klaw/download/klaw-1.3.1.tgz"
  integrity sha1-QIhDO0azsbolnXh4XY6W9zugJDk=
  optionalDependencies:
    graceful-fs "^4.1.9"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/kleur/download/kleur-3.0.3.tgz"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/leven/download/leven-3.1.0.tgz"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/levn/download/levn-0.4.1.tgz"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/levn/download/levn-0.3.0.tgz"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/locate-path/download/locate-path-5.0.0.tgz"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/locate-path/download/locate-path-6.0.0.tgz"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmmirror.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz"
  integrity "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ= sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ=="

lodash@^4.17.10, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.7.0:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

logkitty@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmmirror.com/logkitty/download/logkitty-0.7.1.tgz"
  integrity "sha1-jo1i9Ahagm6NOJh3IlcCNOM8aqc= sha512-/3ER20CTTbahrCrpYfPn7Xavv9diBROZpoXGVZDWMw4b/X4uuUwAC0ki85tgsdMRONURyIJbcOvS94QsUBYPbQ=="
  dependencies:
    ansi-fragments "^0.2.1"
    dayjs "^1.8.15"
    yargs "^15.1.0"

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz?cache=0&sync_timestamp=1632451810665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floose-envify%2Fdownload%2Floose-envify-1.4.0.tgz"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/lru-cache/download/lru-cache-6.0.0.tgz"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

make-dir@^2.0.0, make-dir@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz"
  integrity "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU= sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA=="
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://registry.npmmirror.com/makeerror/download/makeerror-1.0.12.tgz?cache=0&sync_timestamp=1635238306211&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmakeerror%2Fdownload%2Fmakeerror-1.0.12.tgz"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

merge-options@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/merge-options/download/merge-options-3.0.4.tgz"
  integrity sha1-hHCcKqKkskwZgfZsF5/lVlzG27c=
  dependencies:
    is-plain-obj "^2.1.0"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/merge-stream/download/merge-stream-2.0.0.tgz"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/merge2/download/merge2-1.4.1.tgz"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

metro-babel-register@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-babel-register/download/metro-babel-register-0.66.2.tgz"
  integrity sha1-xrvjbHp3WQaHzNdLQl3AINF9Ba8=
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/register" "^7.0.0"
    escape-string-regexp "^1.0.5"

metro-babel-transformer@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-babel-transformer/download/metro-babel-transformer-0.66.2.tgz"
  integrity "sha1-/OCj4xTSil5xQcE1Zl4cybjnzoY= sha512-aJ/7fc/Xkofw8Fqa51OTDhBzBz26mmpIWrXAZcPdQ8MSTt883EWncxeCEjasc79NJ89BRi7sOkkaWZo2sXlKvw=="
  dependencies:
    "@babel/core" "^7.14.0"
    hermes-parser "0.4.7"
    metro-source-map "0.66.2"
    nullthrows "^1.1.1"

metro-cache-key@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-cache-key/download/metro-cache-key-0.66.2.tgz"
  integrity "sha1-1kY9KlPoh6OEGdUjlizCTqDngLQ= sha512-WtkNmRt41qOpHh1MkNA4nLiQ/m7iGL90ysSKD+fcLqlUnOBKJptPQm0ZUv8Kfqk18ddWX2KmsSbq+Sf3I6XohQ=="

metro-cache@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-cache/download/metro-cache-0.66.2.tgz"
  integrity "sha1-4K9OCjGYmPfUKpgPfuXaFT/P0Bk= sha512-5QCYJtJOHoBSbL3H4/Fpl36oA697C3oYHqsce+Hk/dh2qtODUGpS3gOBhvP1B8iB+H8jJMyR75lZq129LJEsIQ=="
  dependencies:
    metro-core "0.66.2"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"

metro-config@0.66.2, metro-config@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-config/download/metro-config-0.66.2.tgz"
  integrity "sha1-42Ws22atDNoBgrnJkQdgqX7kKTs= sha512-0C+PrKKIBNNzLZUKN/8ZDJS2U5FLMOTXDWbvBHIdqb6YXz8WplXR2+xlSlaSCCi5b+GR7cWFWUNeKA4GQS1/AQ=="
  dependencies:
    cosmiconfig "^5.0.5"
    jest-validate "^26.5.2"
    metro "0.66.2"
    metro-cache "0.66.2"
    metro-core "0.66.2"
    metro-runtime "0.66.2"

metro-core@0.66.2, metro-core@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-core/download/metro-core-0.66.2.tgz"
  integrity "sha1-6td2oXs+WjB+bcIiWdswv1x+hJA= sha512-JieLZkef/516yxXYvQxWnf3OWw5rcgWRy76K8JV/wr/i8LGVGulPAXlIi445/QZzXVydzRVASKAEVqyxM5F4mA=="
  dependencies:
    jest-haste-map "^26.5.2"
    lodash.throttle "^4.1.1"
    metro-resolver "0.66.2"

metro-hermes-compiler@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-hermes-compiler/download/metro-hermes-compiler-0.66.2.tgz"
  integrity "sha1-MCkHSPg4BfqmAapIdjJESRV5WCM= sha512-nCVL1g9uR6vrw5+X1wjwZruRyMkndnzGRMqjqoljf+nGEqBTD607CR7elXw4fMWn/EM+1y0Vdq5altUu9LdgCA=="

metro-inspector-proxy@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-inspector-proxy/download/metro-inspector-proxy-0.66.2.tgz"
  integrity "sha1-qDx2vS8v17kkC+kqz5qLHRQEVHo= sha512-gnLc9121eznwP0iiA9tCBW8qZjwIsCgwHWMF1g1Qaki9le9tzeJv3dK4/lFNGxyfSaLO7vahQEhsEYsiRnTROg=="
  dependencies:
    connect "^3.6.5"
    debug "^2.2.0"
    ws "^1.1.5"
    yargs "^15.3.1"

metro-minify-uglify@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-minify-uglify/download/metro-minify-uglify-0.66.2.tgz"
  integrity "sha1-YGHb7k9h5tW7PBAOQ3n/by4W5Cs= sha512-7TUK+L5CmB5x1PVnFbgmjzHW4CUadq9H5jgp0HfFoWT1skXAyEsx0DHkKDXwnot0khnNhBOEfl62ctQOnE110Q=="
  dependencies:
    uglify-es "^3.1.9"

metro-react-native-babel-preset@0.66.2, metro-react-native-babel-preset@^0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-react-native-babel-preset/download/metro-react-native-babel-preset-0.66.2.tgz"
  integrity sha1-/d689BOtTqYX1PR/fB2kAQUt5zQ=
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-async-to-generator" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    react-refresh "^0.4.0"

metro-react-native-babel-transformer@0.66.2, metro-react-native-babel-transformer@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-react-native-babel-transformer/download/metro-react-native-babel-transformer-0.66.2.tgz"
  integrity sha1-do80Hnw9PRw4GJeZyYhLkNHDLrc=
  dependencies:
    "@babel/core" "^7.14.0"
    babel-preset-fbjs "^3.4.0"
    hermes-parser "0.4.7"
    metro-babel-transformer "0.66.2"
    metro-react-native-babel-preset "0.66.2"
    metro-source-map "0.66.2"
    nullthrows "^1.1.1"

metro-resolver@0.66.2, metro-resolver@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-resolver/download/metro-resolver-0.66.2.tgz"
  integrity "sha1-90PdvnoS3RN9H3pVVzLK/K6kIfg= sha512-pXQAJR/xauRf4kWFj2/hN5a77B4jLl0Fom5I3PHp6Arw/KxSBp0cnguXpGLwNQ6zQC0nxKCoYGL9gQpzMnN7Hw=="
  dependencies:
    absolute-path "^0.0.0"

metro-runtime@0.66.2, metro-runtime@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-runtime/download/metro-runtime-0.66.2.tgz"
  integrity sha1-NAnulXuUm2x7cu9u0rmvmk9KkQ4=

metro-source-map@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-source-map/download/metro-source-map-0.66.2.tgz"
  integrity sha1-tTBKKCpdVfpntZkmXpzzIXF1zdc=
  dependencies:
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    invariant "^2.2.4"
    metro-symbolicate "0.66.2"
    nullthrows "^1.1.1"
    ob1 "0.66.2"
    source-map "^0.5.6"
    vlq "^1.0.0"

metro-symbolicate@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-symbolicate/download/metro-symbolicate-0.66.2.tgz"
  integrity "sha1-rd0JXOX3fnPKId2137OW/11PoEE= sha512-u+DeQHyAFXVD7mVP+GST/894WHJ3i/U8oEJFnT7U3P52ZuLgX8n4tMNxhqZU12RcLR6etF8143aP0Ktx1gFLEQ=="
  dependencies:
    invariant "^2.2.4"
    metro-source-map "0.66.2"
    nullthrows "^1.1.1"
    source-map "^0.5.6"
    through2 "^2.0.1"
    vlq "^1.0.0"

metro-transform-plugins@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-transform-plugins/download/metro-transform-plugins-0.66.2.tgz"
  integrity "sha1-Od0ESiOxND5PLS7DTQgSjN8lXtQ= sha512-KTvqplh0ut7oDKovvDG6yzXM02R6X+9b2oVG+qYq8Zd3aCGTi51ASx4ThCNkAHyEvCuJdYg9fxXTL+j+wvhB5w=="
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    nullthrows "^1.1.1"

metro-transform-worker@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro-transform-worker/download/metro-transform-worker-0.66.2.tgz"
  integrity "sha1-CoRVmSEyxHlyGszVLJvUfet3dp4= sha512-dO4PtYOMGB7Vzte8aIzX39xytODhmbJrBYPu+zYzlDjyefJZT7BkZ0LkPIThtyJi96xWcGqi9JBSo0CeRupAHw=="
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/types" "^7.0.0"
    babel-preset-fbjs "^3.4.0"
    metro "0.66.2"
    metro-babel-transformer "0.66.2"
    metro-cache "0.66.2"
    metro-cache-key "0.66.2"
    metro-hermes-compiler "0.66.2"
    metro-source-map "0.66.2"
    metro-transform-plugins "0.66.2"
    nullthrows "^1.1.1"

metro@0.66.2, metro@^0.66.1:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/metro/download/metro-0.66.2.tgz"
  integrity "sha1-8hdZvwCZVHDnV3tbiKUneWPyRJI= sha512-uNsISfcQ3iKKSHoN5Q+LAh0l3jeeg7ZcNZ/4BAHGsk02erA0OP+l2m+b5qYVoPptHz9Oc3KyG5oGJoTu41pWjg=="
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.14.0"
    "@babel/generator" "^7.14.0"
    "@babel/parser" "^7.14.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.14.0"
    "@babel/types" "^7.0.0"
    absolute-path "^0.0.0"
    accepts "^1.3.7"
    async "^2.4.0"
    chalk "^4.0.0"
    ci-info "^2.0.0"
    connect "^3.6.5"
    debug "^2.2.0"
    denodeify "^1.2.1"
    error-stack-parser "^2.0.6"
    fs-extra "^1.0.0"
    graceful-fs "^4.1.3"
    hermes-parser "0.4.7"
    image-size "^0.6.0"
    invariant "^2.2.4"
    jest-haste-map "^26.5.2"
    jest-worker "^26.0.0"
    lodash.throttle "^4.1.1"
    metro-babel-register "0.66.2"
    metro-babel-transformer "0.66.2"
    metro-cache "0.66.2"
    metro-cache-key "0.66.2"
    metro-config "0.66.2"
    metro-core "0.66.2"
    metro-hermes-compiler "0.66.2"
    metro-inspector-proxy "0.66.2"
    metro-minify-uglify "0.66.2"
    metro-react-native-babel-preset "0.66.2"
    metro-resolver "0.66.2"
    metro-runtime "0.66.2"
    metro-source-map "0.66.2"
    metro-symbolicate "0.66.2"
    metro-transform-plugins "0.66.2"
    metro-transform-worker "0.66.2"
    mime-types "^2.1.27"
    mkdirp "^0.5.1"
    node-fetch "^2.2.0"
    nullthrows "^1.1.1"
    rimraf "^2.5.4"
    serialize-error "^2.1.0"
    source-map "^0.5.6"
    strip-ansi "^6.0.0"
    temp "0.8.3"
    throat "^5.0.0"
    ws "^1.1.5"
    yargs "^15.3.1"

micromatch@^3.1.10, micromatch@^3.1.4:
  version "3.1.10"
  resolved "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.4.tgz"
  integrity sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

mime-db@1.50.0, "mime-db@>= 1.43.0 < 2":
  version "1.50.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.50.0.tgz"
  integrity sha1-q9SslOmNPA4YUBbGerRdX95AwR8=

mime-types@^2.1.12, mime-types@^2.1.27, mime-types@~2.1.24:
  version "2.1.33"
  resolved "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108315670&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz"
  integrity sha1-H6EqkERy+v0GjkjZ6EAfdNP3Dts=
  dependencies:
    mime-db "1.50.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz?cache=0&sync_timestamp=1635900684619&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime%2Fdownload%2Fmime-1.6.0.tgz"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@^2.4.1:
  version "2.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-2.6.0.tgz?cache=0&sync_timestamp=1635900684619&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime%2Fdownload%2Fmime-2.6.0.tgz"
  integrity sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.2, minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.1.1, minimist@^1.2.0, minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.1, mkdirp@^0.5.3:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

moment@^2.29.1:
  version "2.29.1"
  resolved "https://registry.npmmirror.com/moment/download/moment-2.29.1.tgz"
  integrity sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0:
  version "2.6.2"
  resolved "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.2.tgz"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

nocache@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/nocache/download/nocache-2.1.0.tgz"
  integrity "sha1-Egyf/sQ7Vymx1d6IzXGqdaC6SR8= sha512-0L9FvHG3nfnnmaEQPjT9xhfN4ISk0A8/2j4M37Np4mcDesJjHgEUfgPhdCyZuFI954tjokaIj/A3NdpFNdEh4Q=="

node-dir@^0.1.17:
  version "0.1.17"
  resolved "https://registry.npmmirror.com/node-dir/download/node-dir-0.1.17.tgz"
  integrity "sha1-X1Zl2TNRM1yqvvjxxVRRbPXx5OU= sha512-tmPX422rYgofd4epzrNoOXiE8XFZYOcCq1vD7MAXCDO+O+zndlA2ztdKKMa+EeuBG5tHETpr4ml4RGgpqDCCAg=="
  dependencies:
    minimatch "^3.0.2"

node-fetch@2.6.1:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.1.tgz"
  integrity "sha1-BFvTI2Mfdu0uK1VXM5RBa2OaAFI= sha512-V4aYg89jEoVRxRb2fJdAg8FHvI7cEyYdVAh94HH0UIK8oJxUfkjlDQN9RbMx+bEjP7+ggMiFRprSti032Oipxw=="

node-fetch@^2.2.0, node-fetch@^2.6.0:
  version "2.6.6"
  resolved "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.6.tgz?cache=0&sync_timestamp=1635730444620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-2.6.6.tgz"
  integrity sha1-F1GnwBg06OFpd1hzLp77burfr4k=
  dependencies:
    whatwg-url "^5.0.0"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/node-int64/download/node-int64-0.4.0.tgz"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz"
  integrity "sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA= sha512-JMaRS9L4wSRIR+6PTVEikTrq/lMGEZR43a48ETeilY0Q0iMwVnccMFrUM1k+tNzmYuIU0Vh710bCUqHX+/+ctQ=="

node-releases@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.6.tgz"
  integrity sha512-PiVXnNuFm5+iYkLBNeq5211hvO38y63T0i2KKh2KnUs3RpzJ+JtODFjkD8yjLwnDkTYF1eKXheUwdssR+NRZdg==

node-stream-zip@^1.9.1:
  version "1.15.0"
  resolved "https://registry.npmmirror.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz"
  integrity "sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o= sha512-LN4fydt9TqhZhThkZIVQnF9cwjU3qmUH9h78Mx/K7d3VvfRqqwthLwJEUOEL0QPZ0XQmNN7be5Ggit5+4dq3Bw=="

normalize-css-color@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/normalize-css-color/download/normalize-css-color-1.0.2.tgz"
  integrity sha1-Apkel8zOxmI/5XOvu/Deah8+n40=

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/normalize-path/download/normalize-path-2.1.1.tgz"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  integrity "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8= sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw=="
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nullthrows@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/nullthrows/download/nullthrows-1.1.1.tgz"
  integrity sha1-eBgliEOFaulx6uQgitfX6xmkMbE=

nwsapi@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/nwsapi/download/nwsapi-2.2.0.tgz"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

ob1@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/ob1/download/ob1-0.66.2.tgz"
  integrity "sha1-jK9UggLPJoiUS65H20BaCLyhemE= sha512-RFewnL/RjE0qQBOuM+2bbY96zmJPIge/aDtsiDbLSb+MOiK8CReAhBHDgL+zrA3F1hQk00lMWpUwYcep750plA=="

object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.11.0, object-inspect@^1.9.0:
  version "1.11.0"
  resolved "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.0.tgz"
  integrity sha1-nc6xRs7dQUig2eUauI00z1CZIrE=

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz"
  integrity sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=
  dependencies:
    call-bind "^1.0.0"
    define-properties "^1.1.3"
    has-symbols "^1.0.1"
    object-keys "^1.1.1"

object.entries@^1.1.4:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/object.entries/download/object.entries-1.1.5.tgz?cache=0&sync_timestamp=1633282037745&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.entries%2Fdownload%2Fobject.entries-1.1.5.tgz"
  integrity sha1-4azdF8TeLNltWghIfPuduE2IGGE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.fromentries@^2.0.4:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/object.fromentries/download/object.fromentries-2.0.5.tgz?cache=0&sync_timestamp=1633282036610&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.fromentries%2Fdownload%2Fobject.fromentries-2.0.5.tgz"
  integrity sha1-ezeyBRCcIedB5gVyf+iwrV+gglE=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.hasown@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/object.hasown/download/object.hasown-1.1.0.tgz"
  integrity sha1-cjLtJm800ZfRXKxYgCMvekeQr+U=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.4:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/object.values/download/object.values-1.1.5.tgz"
  integrity sha1-lZ9j486e8QhyAzMIITHkpFm3Fqw=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/on-headers/download/on-headers-1.0.2.tgz"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/once/download/once-1.4.0.tgz"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/onetime/download/onetime-5.1.2.tgz"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.2.0:
  version "6.4.0"
  resolved "https://registry.npmmirror.com/open/download/open-6.4.0.tgz"
  integrity "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk= sha512-IFenVPgF70fSm1keSd2iDBIDIBZkroLeuffXq+wKTzTJlBpesFWojV9lb8mzOfaAzM1sr7HQHuO0vtV0zYekGg=="
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.8.1:
  version "0.8.3"
  resolved "https://registry.npmmirror.com/optionator/download/optionator-0.8.3.tgz"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npmmirror.com/optionator/download/optionator-0.9.1.tgz"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

options@>=0.0.5:
  version "0.0.6"
  resolved "https://registry.npmmirror.com/options/download/options-0.0.6.tgz"
  integrity "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8= sha512-bOj3L1ypm++N+n7CEbbe473A414AB7z+amKYshRb//iuL3MpdDCLhPnw6aVTdKB9g5ZRVHIEp8eUln6L2NUStg=="

ora@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/ora/download/ora-3.4.0.tgz"
  integrity "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg= sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg=="
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-tmpdir@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/p-limit/download/p-limit-3.1.0.tgz"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/p-locate/download/p-locate-4.1.0.tgz"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/p-locate/download/p-locate-5.0.0.tgz"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1636011996917&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-4.0.0.tgz"
  integrity "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA= sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw=="
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse5@6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/parse5/download/parse5-6.0.1.tgz"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-exists/download/path-exists-4.0.0.tgz"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.7.tgz?cache=0&sync_timestamp=1632469101267&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpath-parse%2Fdownload%2Fpath-parse-1.0.7.tgz"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@^1.8.0:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz"
  integrity "sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo= sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA=="
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-type/download/path-type-4.0.0.tgz"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093437726&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.3:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/picomatch/download/picomatch-2.3.0.tgz"
  integrity sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/pify/download/pify-4.0.1.tgz"
  integrity "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE= sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g=="

pirates@^4.0.0, pirates@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz"
  integrity "sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c= sha512-WuNqLTbMI3tmfef2TKxlQmAiLHKtFhlsCZnPIpuv2Ow0RDVO8lfy1Opf4NUzlMXLjPl+Men7AuVdX6TA+s+uGA=="
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz?cache=0&sync_timestamp=1633498184785&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-3.0.0.tgz"
  integrity "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM= sha512-/E57AYkoeQ25qkxMj5PBOVgF8Kiu/h7cYS30Z5+R7WaiCCBfLq58ZI/dSeaEKb9WVJV5n/03QwrN3IeWIFllvw=="
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1633498537676&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-up@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/pkg-up/download/pkg-up-3.1.0.tgz"
  integrity sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU=
  dependencies:
    find-up "^3.0.0"

plist@^3.0.2, plist@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/plist/download/plist-3.0.4.tgz"
  integrity "sha1-pi34N+Ou0rs7c1iZ1RDE8YYBnL4= sha512-ksrr8y9+nXOxQB2osVNqrgvX/XQPOXaU4BQMKjYq8PvaY1U18mo+fKgBSwzK+luSyinOuPae956lSVcBwxlAMg=="
  dependencies:
    base64-js "^1.5.1"
    xmlbuilder "^9.0.7"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  integrity "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s= sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w=="
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.0.2:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/prettier/download/prettier-2.4.1.tgz"
  integrity sha1-Zx4RyJwUpM/Ids5WQQbEpnJsn1w=

pretty-format@^26.5.2, pretty-format@^26.6.2:
  version "26.6.2"
  resolved "https://registry.npmmirror.com/pretty-format/download/pretty-format-26.6.2.tgz?cache=0&sync_timestamp=1634626739813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-format%2Fdownload%2Fpretty-format-26.6.2.tgz"
  integrity sha1-41wnBfFMt/4v6U+geDRbREEg/JM=
  dependencies:
    "@jest/types" "^26.6.2"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^17.0.1"

pretty-format@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/pretty-format/download/pretty-format-27.3.1.tgz?cache=0&sync_timestamp=1634626739813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-format%2Fdownload%2Fpretty-format-27.3.1.tgz"
  integrity "sha1-fpSGNlzN1KUCBh+nYdOrnKG3jfU= sha512-DR/c+pvFc52nLimLROYjnXPtolawm+uWDxr4FjuLDLUn+ktWnSN851KoHwHzzqq6rfCOjkzN8FLgDrSub6UDuA=="
  dependencies:
    "@jest/types" "^27.2.5"
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/progress/download/progress-2.0.3.tgz"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npmmirror.com/promise/download/promise-7.3.1.tgz"
  integrity "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078= sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg=="
  dependencies:
    asap "~2.0.3"

promise@^8.0.3:
  version "8.1.0"
  resolved "https://registry.npmmirror.com/promise/download/promise-8.1.0.tgz"
  integrity sha1-aXwlw9/nQ13Xn81Yw4oTWIjq8F4=
  dependencies:
    asap "~2.0.6"

prompts@^2.0.1, prompts@^2.4.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/prompts/download/prompts-2.4.2.tgz"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.6.2, prop-types@^15.7.2:
  version "15.7.2"
  resolved "https://registry.npmmirror.com/prop-types/download/prop-types-15.7.2.tgz"
  integrity sha1-UsQedbjIfnK52TYOAga5ncv/psU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

psl@^1.1.33:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/psl/download/psl-1.8.0.tgz"
  integrity sha1-kyb4vPsBOtzABf3/BWrM4CDlHCQ=

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

query-string@^6.13.6:
  version "6.14.1"
  resolved "https://registry.npmmirror.com/query-string/download/query-string-6.14.1.tgz"
  integrity "sha1-esLcpG2n8wlEm6D4ax/SglWwyGo= sha512-XDxAeVmpfu1/6IjyT/gXHOl+S0vQ9owggJ30hhWKdHAsNPOcasn5o9BW0eejZqL2e4vMjhAxoW3jVHcD6mbcYw=="
  dependencies:
    decode-uri-component "^0.2.0"
    filter-obj "^1.1.0"
    split-on-first "^1.0.0"
    strict-uri-encode "^2.0.0"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

react-devtools-core@^4.13.0:
  version "4.21.0"
  resolved "https://registry.npmmirror.com/react-devtools-core/download/react-devtools-core-4.21.0.tgz"
  integrity sha1-pUyaD9cmFJHmFtbIfRhp4BHYUh0=
  dependencies:
    shell-quote "^1.6.1"
    ws "^7"

"react-is@^16.12.0 || ^17.0.0", react-is@^17.0.1, react-is@^17.0.2:
  version "17.0.2"
  resolved "https://registry.npmmirror.com/react-is/download/react-is-17.0.2.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-17.0.2.tgz"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^16.13.0, react-is@^16.7.0, react-is@^16.8.1:
  version "16.13.1"
  resolved "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-16.13.1.tgz"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-native-camera@^4.2.1:
  version "4.2.1"
  resolved "https://registry.npmmirror.com/react-native-camera/-/react-native-camera-4.2.1.tgz#caf74081f055e89d7e9b0cd5108965d808c60e90"
  integrity sha512-+Vkql24PFYQfsPRznJCvPwJQfyzCnjlcww/iZ4Ej80bgivKjL9eU0IMQIXp4yi6XCrKi4voWXxIDPMupQZKeIQ==
  dependencies:
    prop-types "^15.6.2"

react-native-codegen@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npmmirror.com/react-native-codegen/download/react-native-codegen-0.0.7.tgz"
  integrity sha1-hmUcXF/sZ6gHfvf0429+1FkEPhQ=
  dependencies:
    flow-parser "^0.121.0"
    jscodeshift "^0.11.0"
    nullthrows "^1.1.1"

react-native-collapsible@^1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/react-native-collapsible/download/react-native-collapsible-1.6.0.tgz"
  integrity sha1-yiYf//FpFPhyBZuwly46eMSzf5w=

react-native-gesture-handler@^1.10.3:
  version "1.10.3"
  resolved "https://registry.npmmirror.com/react-native-gesture-handler/download/react-native-gesture-handler-1.10.3.tgz"
  integrity sha1-lCu/KWO79J+nlZNgDunXtdqzz8A=
  dependencies:
    "@egjs/hammerjs" "^2.0.17"
    fbjs "^3.0.0"
    hoist-non-react-statics "^3.3.0"
    invariant "^2.2.4"
    prop-types "^15.7.2"

react-native-iphone-x-helper@^1.3.0:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/react-native-iphone-x-helper/download/react-native-iphone-x-helper-1.3.1.tgz"
  integrity sha1-IMYD6aDnZf1vlzlmOL3rDlpgsBA=

react-native-modal-popover@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/react-native-modal-popover/download/react-native-modal-popover-2.1.0.tgz"
  integrity sha1-RaIGABJ5bykYTmxBt4fxQzbTtDU=
  dependencies:
    lodash "^4.17.21"
    prop-types "^15.7.2"

react-native-nfc-manager@^3.11.0:
  version "3.11.0"
  resolved "https://registry.npmmirror.com/react-native-nfc-manager/download/react-native-nfc-manager-3.11.0.tgz"
  integrity sha1-y1LgSwKcvNALxXz71bo42Clj82o=
  dependencies:
    "@expo/config-plugins" "^3.0.6"

react-native-pager-view@^5.4.8:
  version "5.4.8"
  resolved "https://registry.npmmirror.com/react-native-pager-view/download/react-native-pager-view-5.4.8.tgz"
  integrity sha1-wIcpieP6nvBdXYz0COQvwdP7vXw=

react-native-safe-area-context@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmmirror.com/react-native-safe-area-context/download/react-native-safe-area-context-3.3.2.tgz"
  integrity sha1-lUmizlgPI3TtsF5J1mEljRuLyu0=

react-native-safe-area-view@^0.14.9:
  version "0.14.9"
  resolved "https://registry.npmmirror.com/react-native-safe-area-view/download/react-native-safe-area-view-0.14.9.tgz"
  integrity "sha1-kO6DgwNwENmlBVqXz5fkwdofDD0= sha512-WII/ulhpVyL/qbYb7vydq7dJAfZRBcEhg4/UWt6F6nAKpLa3gAceMOxBxI914ppwSP/TdUsandFy6lkJQE0z4A=="
  dependencies:
    hoist-non-react-statics "^2.3.1"

react-native-webview@^11.14.2:
  version "11.14.2"
  resolved "https://registry.npmmirror.com/react-native-webview/download/react-native-webview-11.14.2.tgz"
  integrity sha1-MeYoZLKPlQ/Yh78gRp/ozXEQHSk=
  dependencies:
    escape-string-regexp "2.0.0"
    invariant "2.2.4"

react-native@0.66.2:
  version "0.66.2"
  resolved "https://registry.npmmirror.com/react-native/download/react-native-0.66.2.tgz"
  integrity sha1-EQNRcqc/CBXChpmx/OOmZR3WNVs=
  dependencies:
    "@jest/create-cache-key-function" "^27.0.1"
    "@react-native-community/cli" "^6.0.0"
    "@react-native-community/cli-platform-android" "^6.0.0"
    "@react-native-community/cli-platform-ios" "^6.0.0"
    "@react-native/assets" "1.0.0"
    "@react-native/normalize-color" "1.0.0"
    "@react-native/polyfills" "2.0.0"
    abort-controller "^3.0.0"
    anser "^1.4.9"
    base64-js "^1.1.2"
    event-target-shim "^5.0.1"
    hermes-engine "~0.9.0"
    invariant "^2.2.4"
    jsc-android "^250230.2.1"
    metro-babel-register "0.66.2"
    metro-react-native-babel-transformer "0.66.2"
    metro-runtime "0.66.2"
    metro-source-map "0.66.2"
    nullthrows "^1.1.1"
    pretty-format "^26.5.2"
    promise "^8.0.3"
    prop-types "^15.7.2"
    react-devtools-core "^4.13.0"
    react-native-codegen "^0.0.7"
    react-refresh "^0.4.0"
    regenerator-runtime "^0.13.2"
    scheduler "^0.20.2"
    stacktrace-parser "^0.1.3"
    use-subscription "^1.0.0"
    whatwg-fetch "^3.0.0"
    ws "^6.1.4"

react-navigation-stack@^2.10.4:
  version "2.10.4"
  resolved "https://registry.npmmirror.com/react-navigation-stack/download/react-navigation-stack-2.10.4.tgz"
  integrity sha1-N3/WUz+K4M9cpaQ1oBFYUfAQMGo=
  dependencies:
    color "^3.1.3"
    react-native-iphone-x-helper "^1.3.0"

react-navigation@^4.4.4:
  version "4.4.4"
  resolved "https://registry.npmmirror.com/react-navigation/download/react-navigation-4.4.4.tgz"
  integrity sha1-jNoiGRljEdtEDlSZi8ckUjNZlJ8=
  dependencies:
    "@react-navigation/core" "^3.7.9"
    "@react-navigation/native" "^3.8.4"

react-refresh@^0.4.0:
  version "0.4.3"
  resolved "https://registry.npmmirror.com/react-refresh/download/react-refresh-0.4.3.tgz?cache=0&sync_timestamp=1636129959244&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-refresh%2Fdownload%2Freact-refresh-0.4.3.tgz"
  integrity sha1-lm8XUMGRZy524Wwu+laRUMxzq1M=

react-shallow-renderer@^16.13.1:
  version "16.14.1"
  resolved "https://registry.npmmirror.com/react-shallow-renderer/download/react-shallow-renderer-16.14.1.tgz?cache=0&sync_timestamp=1632814684562&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-shallow-renderer%2Fdownload%2Freact-shallow-renderer-16.14.1.tgz"
  integrity sha1-vw0C34pRmlWP2bghVELvpchA4SQ=
  dependencies:
    object-assign "^4.1.1"
    react-is "^16.12.0 || ^17.0.0"

react-test-renderer@17.0.2:
  version "17.0.2"
  resolved "https://registry.npmmirror.com/react-test-renderer/download/react-test-renderer-17.0.2.tgz"
  integrity sha1-TNSuXvGtVnD8Dvd26Mx+EjHZhmw=
  dependencies:
    object-assign "^4.1.1"
    react-is "^17.0.2"
    react-shallow-renderer "^16.13.1"
    scheduler "^0.20.2"

react@17.0.2:
  version "17.0.2"
  resolved "https://registry.npmmirror.com/react/download/react-17.0.2.tgz"
  integrity sha1-0LXMUW0p6z7uOD91tihkz7aAADc=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

readable-stream@~2.3.6:
  version "2.3.7"
  resolved "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz?cache=0&sync_timestamp=1632380409088&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freadable-stream%2Fdownload%2Freadable-stream-2.3.7.tgz"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readline@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/readline/download/readline-1.3.0.tgz"
  integrity "sha1-xYDXfvLPyHUrEySYBg3JeTp6wBw= sha512-k2d6ACCkiNYz222Fs/iNze30rRJ1iIicW7JuX/7/cozvih6YCkFZH+J6mAFDVgv0dRBaAyr4jDqC95R2y4IADg=="

recast@^0.20.3:
  version "0.20.5"
  resolved "https://registry.npmmirror.com/recast/download/recast-0.20.5.tgz?cache=0&sync_timestamp=1632753969639&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Frecast%2Fdownload%2Frecast-0.20.5.tgz"
  integrity "sha1-jixsloJ6GzOcY03SMpV9IwVTzq4= sha512-E5qICoPoNL4yU0H0NoBDntNB0Q5oMSNh9usFctYniLBluTthi3RsQVBXIJNbApOlvSwW/RGxIuokPcAc59J5fQ=="
  dependencies:
    ast-types "0.14.2"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tslib "^2.0.1"

regenerate-unicode-properties@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmmirror.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz"
  integrity sha512-d1VudCLoIGitcU/hEg2QqvyGZQmdC0Lf8BqdOMXGFSvJP4bNV1+XqbPQeHHLD51Jh4QJJ225dlIFvY4Ly6MXmQ==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.2.tgz"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.2, regenerator-runtime@^0.13.4:
  version "0.13.9"
  resolved "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz"
  integrity sha1-iSV0Kpj/2QgUmI11Zq0wyjsmO1I=

regenerator-transform@^0.15.0:
  version "0.15.1"
  resolved "https://registry.npmmirror.com/regenerator-transform/-/regenerator-transform-0.15.1.tgz"
  integrity sha512-knzmNAcuyxV+gQCufkYcvOqX/qIIfHLv0u5x79kRxuGojfYVky1f15TzZEu2Avte8QGepvUNTnLskf8E6X6Vyg==
  dependencies:
    "@babel/runtime" "^7.8.4"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.3.1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz"
  integrity sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpp@^3.1.0, regexpp@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/regexpp/download/regexpp-3.2.0.tgz"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

regexpu-core@^5.1.0:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/regexpu-core/-/regexpu-core-5.2.2.tgz"
  integrity sha512-T0+1Zp2wjF/juXMrMxHxidqGYn8U4R+zleSJhX9tQ1PUsS8a9UtYfbsF9LdiVgNX3kiX8RNaKM42nfSgvFJjmw==
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsgen "^0.7.1"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmmirror.com/regjsgen/-/regjsgen-0.7.1.tgz"
  integrity sha512-RAt+8H2ZEzHeYWxZ3H2z6tF18zyyOnlcdaafLrm21Bguj7uZy6ULibiAFdXEtKQY4Sy7wDTwDiOazasMLc4KPA==

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npmmirror.com/regjsparser/-/regjsparser-0.9.1.tgz"
  integrity sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.4.tgz"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

reselect@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/reselect/download/reselect-4.1.2.tgz"
  integrity sha1-e/ZCmS0UPU87Dy3KiqUgGICKHVE=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/download/resolve-from-3.0.0.tgz"
  integrity "sha1-six699nWiBvItuZTM17rywoYh0g= sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw=="

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/download/resolve-from-4.0.0.tgz"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/download/resolve-from-5.0.0.tgz"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve.exports@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/resolve.exports/download/resolve.exports-1.1.0.tgz"
  integrity sha1-XOhCuUsFFGwOAwdphdHQ5+SMkMk=

resolve@^1.12.0, resolve@^1.13.1, resolve@^1.14.2, resolve@^1.20.0, resolve@^1.3.2:
  version "1.20.0"
  resolved "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

resolve@^2.0.0-next.3:
  version "2.0.0-next.3"
  resolved "https://registry.npmmirror.com/resolve/download/resolve-2.0.0-next.3.tgz"
  integrity sha1-1BAWKT1KhYajnKXZtfFcvqH1XkY=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/reusify/download/reusify-1.0.4.tgz"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@^2.5.4:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rimraf@~2.2.6:
  version "2.2.8"
  resolved "https://registry.npmmirror.com/rimraf/download/rimraf-2.2.8.tgz"
  integrity "sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI= sha512-R5KMKHnPAQaZMqLOsyuyUmcIjSeDm+73eoqQpaXA7AZ22BL+6C+1mcUscgOsNd8WVlJuvlgAPsegcx7pjlV0Dg=="

rsvp@^4.8.4:
  version "4.8.5"
  resolved "https://registry.npmmirror.com/rsvp/download/rsvp-4.8.5.tgz"
  integrity "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ= sha512-nfMOlASu9OnRJo1mbEk2cz0D56a1MBNrJ7orjRZQG10XDyuvwksKbuXNp6qa+kbn839HwjwhBzhFmdsaEAfauA=="

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/run-parallel/download/run-parallel-1.2.0.tgz"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/sane/download/sane-4.1.0.tgz"
  integrity "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0= sha512-hhbzAgTIX8O7SHfp2c8/kREfEn4qO/9q8C9beyY6+tvZ87EpoZ3i1RIEvp27YBswnNbY9mWd6paKVmKbAgLfZA=="
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sax@>=0.6.0, sax@^1.2.1:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

saxes@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/saxes/download/saxes-5.0.1.tgz"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmmirror.com/scheduler/download/scheduler-0.20.2.tgz"
  integrity sha1-S67jlDbjSqk7SHS93L8P6Li1DpE=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

semver@^5.3.0, semver@^5.5.0, semver@^5.6.0:
  version "5.7.1"
  resolved "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.0.0, semver@^6.1.1, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.2.1, semver@^7.3.2, semver@^7.3.5:
  version "7.3.5"
  resolved "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1632728822118&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz"
  integrity sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=
  dependencies:
    lru-cache "^6.0.0"

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.npmmirror.com/send/download/send-0.17.1.tgz"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-error@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/serialize-error/download/serialize-error-2.1.0.tgz"
  integrity "sha1-ULZ51WNc34Rme9yOWa9OW4HV9go= sha512-ghgmKt5o4Tly5yEG/UJp8qTd0AN7Xalw4XBtDEKP655B699qMEtra1WlXeE6WIvdEG481JvRxULKsInq/iNysw=="

serve-static@^1.13.1:
  version "1.14.1"
  resolved "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.1.tgz"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  integrity "sha1-jymBrZJTH1UDWwH7IwdppA4C76M= sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA=="
  dependencies:
    kind-of "^6.0.2"

shallowequal@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/shallowequal/download/shallowequal-1.1.0.tgz"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/shebang-command/download/shebang-command-2.0.0.tgz"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@1.6.1, shell-quote@^1.6.1:
  version "1.6.1"
  resolved "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.6.1.tgz?cache=0&sync_timestamp=1634798333958&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fshell-quote%2Fdownload%2Fshell-quote-1.6.1.tgz"
  integrity "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c= sha512-V0iQEZ/uoem3NmD91rD8XiuozJnq9/ZJnbHVXHnWqP1ucAhS3yJ7sLIIzEi57wFFcK3oi3kFUC46uSyWr35mxg=="
  dependencies:
    array-filter "~0.0.0"
    array-map "~0.0.0"
    array-reduce "~0.0.0"
    jsonify "~0.0.0"

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.4.tgz"
  integrity sha1-785cj9wQTudRslxY1CkAEfpeos8=
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.5"
  resolved "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632949289206&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz"
  integrity "sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8= sha512-KWcOiKeQj6ZyXx7zq4YxSMgHRlod4czeBQZrPb8OKcohcqAXShm7E20kEMle9WBt26hFcAf0qLOcp5zmY7kOqQ=="

simple-plist@^1.0.0, simple-plist@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/simple-plist/download/simple-plist-1.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsimple-plist%2Fdownload%2Fsimple-plist-1.3.0.tgz"
  integrity "sha1-9FGZdmPq/Y6mutNToByvSe8YbUM= sha512-uYWpeGFtZtVt2NhG4AHgpwx323zxD85x42heMJBan1qAiqqozIlaGrwrEt6kRjXWRWIXsuV1VLCvVmZan2B5dg=="
  dependencies:
    bplist-creator "0.1.0"
    bplist-parser "0.3.0"
    plist "^3.0.4"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/sisteransi/download/sisteransi-1.0.5.tgz"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/slash/download/slash-3.0.0.tgz"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/slice-ansi/download/slice-ansi-2.1.0.tgz?cache=0&sync_timestamp=1632753426896&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fslice-ansi%2Fdownload%2Fslice-ansi-2.1.0.tgz"
  integrity "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY= sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ=="
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.16, source-map-support@^0.5.6:
  version "0.5.20"
  resolved "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.20.tgz"
  integrity sha1-EhZgifj15ejFaSazd2Mzkt0stsk=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.1.tgz"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.0, source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "https://registry.npmmirror.com/source-map/download/source-map-0.7.3.tgz"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

split-on-first@^1.0.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/split-on-first/download/split-on-first-1.1.0.tgz"
  integrity "sha1-9hCv7uOxK84dDDBCXnY5i3gkml8= sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw=="

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stack-utils@^2.0.3:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/stack-utils/download/stack-utils-2.0.5.tgz"
  integrity sha1-0lJl/KmVFUZZ27+6O0klR3jS/dU=
  dependencies:
    escape-string-regexp "^2.0.0"

stackframe@^1.1.1:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/stackframe/download/stackframe-1.2.0.tgz"
  integrity sha1-UkKUktY8YuuYmATBFVLj0i53kwM=

stacktrace-parser@^0.1.3:
  version "0.1.10"
  resolved "https://registry.npmmirror.com/stacktrace-parser/download/stacktrace-parser-0.1.10.tgz"
  integrity sha1-KfsMrk4NC4UVWHlAKFehY562BRo=
  dependencies:
    type-fest "^0.7.1"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

stream-buffers@2.2.x:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/stream-buffers/download/stream-buffers-2.2.0.tgz"
  integrity "sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ= sha512-uyQK/mx5QjHun80FLJTfaWE7JtwfRMKBLkMne6udYOmvH0CawotVa7TfgYHzAnpphn4+TweIx1QKMnRIbipmUg=="

strict-uri-encode@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz"
  integrity "sha1-ucczDHBChi9rFC3CdLvMWGbONUY= sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ=="

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/string-length/download/string-length-4.0.2.tgz"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string.prototype.matchall@^4.0.5:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/string.prototype.matchall/download/string.prototype.matchall-4.0.6.tgz"
  integrity sha1-Wrtdq8lMew6iOA9lumELOlRLFfo=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"
    es-abstract "^1.19.1"
    get-intrinsic "^1.1.1"
    has-symbols "^1.0.2"
    internal-slot "^1.0.3"
    regexp.prototype.flags "^1.3.1"
    side-channel "^1.0.4"

string.prototype.trimend@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz"
  integrity sha1-51rpDClCxjUEaGwYsoe0oLGkX4A=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string.prototype.trimstart@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz"
  integrity sha1-s2OZr0qymZtMnGSL16P7K7Jv7u0=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^5.0.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-bom/download/strip-bom-4.0.0.tgz"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  integrity "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8= sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q=="

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

sudo-prompt@^9.0.0:
  version "9.2.1"
  resolved "https://registry.npmmirror.com/sudo-prompt/download/sudo-prompt-9.2.1.tgz"
  integrity "sha1-d++4QwnJykiVJ6TnSfKH5r3VKv0= sha512-Mu7R0g4ig9TUuGSxJavny5Rv0egCEtpZRNMrZaYS1vxkiIxGiGUwoezU3LazIQ+KE04hTrTfNPgxU5gzi7F5Pw=="

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-8.1.1.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-8.1.1.tgz"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/supports-hyperlinks/download/supports-hyperlinks-2.2.0.tgz"
  integrity sha1-T3e0JIh2WJF3S3DHm6vYf5vVlLs=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "https://registry.npmmirror.com/symbol-tree/download/symbol-tree-3.2.4.tgz?cache=0&sync_timestamp=1632753453543&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsymbol-tree%2Fdownload%2Fsymbol-tree-3.2.4.tgz"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

temp@0.8.3, temp@^0.8.1:
  version "0.8.3"
  resolved "https://registry.npmmirror.com/temp/download/temp-0.8.3.tgz"
  integrity "sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k= sha512-jtnWJs6B1cZlHs9wPG7BrowKxZw/rf6+UpGAkr8AaYmiTyTO7zQlLoST8zx/8TcUPnZmeBoB+H8ARuHZaSijVw=="
  dependencies:
    os-tmpdir "^1.0.0"
    rimraf "~2.2.6"

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/terminal-link/download/terminal-link-2.1.1.tgz"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/test-exclude/download/test-exclude-6.0.0.tgz"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/text-table/download/text-table-0.2.0.tgz"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throat@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/throat/download/throat-5.0.0.tgz"
  integrity "sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks= sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA=="

throat@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/throat/download/throat-6.0.1.tgz"
  integrity sha1-1RT+2tlXQMEsLX/HDqhj61Gt43U=

through2@^2.0.1:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/through2/download/through2-2.0.5.tgz"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/tmpl/download/tmpl-1.0.5.tgz"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

tough-cookie@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-4.0.0.tgz?cache=0&sync_timestamp=1632456062832&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftough-cookie%2Fdownload%2Ftough-cookie-4.0.0.tgz"
  integrity sha1-2CIjTuyogvmR8PkIgkrSYi3b7OQ=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.1.2"

tr46@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/tr46/download/tr46-2.1.0.tgz"
  integrity sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=
  dependencies:
    punycode "^2.1.1"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmmirror.com/tr46/download/tr46-0.0.3.tgz"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tslib@^1.13.0, tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/tslib/download/tslib-2.3.1.tgz"
  integrity "sha1-6KM1rdXOrlGqJh0ypJAVjvBC7wE= sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="

tslint@^6.1.3:
  version "6.1.3"
  resolved "https://registry.npmmirror.com/tslint/download/tslint-6.1.3.tgz"
  integrity sha1-XCOy7MwySH1VI706Rw6aoxeJ2QQ=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    builtin-modules "^1.1.1"
    chalk "^2.3.0"
    commander "^2.12.1"
    diff "^4.0.1"
    glob "^7.1.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    mkdirp "^0.5.3"
    resolve "^1.3.2"
    semver "^5.3.0"
    tslib "^1.13.0"
    tsutils "^2.29.0"

tsutils@^2.29.0:
  version "2.29.0"
  resolved "https://registry.npmmirror.com/tsutils/download/tsutils-2.29.0.tgz?cache=0&sync_timestamp=1632753977026&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftsutils%2Fdownload%2Ftsutils-2.29.0.tgz"
  integrity "sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k= sha512-g5JVHCIJwzfISaXpXE1qvNalca5Jwob6FjI4AoPlqMusJ6ftFE7IkkFoMhVLRgK+4Kx3gkzb8UZK5t5yTTvEmA=="
  dependencies:
    tslib "^1.8.1"

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmmirror.com/tsutils/download/tsutils-3.21.0.tgz?cache=0&sync_timestamp=1632753977026&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftsutils%2Fdownload%2Ftsutils-3.21.0.tgz"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/type-check/download/type-check-0.4.0.tgz"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.npmmirror.com/type-check/download/type-check-0.3.2.tgz"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1635390799031&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1635390799031&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.7.1:
  version "0.7.1"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.7.1.tgz"
  integrity sha1-jdpl/q8D7Xjwo/lnjxhpFH98XEg=

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

ua-parser-js@^0.7.30:
  version "0.7.31"
  resolved "https://registry.npmmirror.com/ua-parser-js/download/ua-parser-js-0.7.31.tgz"
  integrity "sha1-ZJplaxkd/6tPIdXgU+J8oXy/9cY= sha512-qLK/Xe9E2uzmYI3qLeOmI0tEOt+TBBQyUIAh4aAgU05FVYzeZrKUdkAZfBNVGRaHVgV0TDkdEngJSw/SyQchkQ=="

uglify-es@^3.1.9:
  version "3.3.9"
  resolved "https://registry.npmmirror.com/uglify-es/download/uglify-es-3.3.9.tgz"
  integrity "sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc= sha512-r+MU0rfv4L/0eeW3xZrd16t4NZfK8Ld4SWVglYBb7ez5uXFWHuVRs6xCTrf1yirs9a4j4Y27nn7SRfO6v67XsQ=="
  dependencies:
    commander "~2.13.0"
    source-map "~0.6.1"

ultron@1.0.x:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/ultron/download/ultron-1.0.2.tgz"
  integrity "sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po= sha512-QMpnpVtYaWEeY+MwKDN/UdKlE/LsFZXM5lO1u7GaZzNgmIbGixHEmVMIKT+vqYOALu3m5GYQy9kz4Xu4IVn7Ow=="

unbox-primitive@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/unbox-primitive/download/unbox-primitive-1.0.1.tgz"
  integrity sha1-CF4hViXsMWJXTciFmr7nilmxRHE=
  dependencies:
    function-bind "^1.1.1"
    has-bigints "^1.0.1"
    has-symbols "^1.0.2"
    which-boxed-primitive "^1.0.2"

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"
  integrity sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.0.0.tgz"
  integrity sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g=

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

universalify@^0.1.0, universalify@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/universalify/download/universalify-1.0.0.tgz"
  integrity "sha1-thodoXPoQ1sv48Z9Kbmt+FlL0W0= sha512-rb6X1W158d7pRQBg5gkR8uPaSfiids68LTJQYOtEUhoJUWBdaQHsuT/EUduxXYxcrt4r5PJ4fuHW1MHT6p0qug=="

universalify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/universalify/download/universalify-2.0.0.tgz"
  integrity sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc=

unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

update-browserslist-db@^1.0.9:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz"
  integrity sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==
  dependencies:
    escalade "^3.1.1"
    picocolors "^1.0.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmmirror.com/uri-js/download/uri-js-4.4.1.tgz"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

use-subscription@^1.0.0:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/use-subscription/download/use-subscription-1.5.1.tgz"
  integrity sha1-c1ARB/AvrYTG3VeWW+sLdcaMQtE=
  dependencies:
    object-assign "^4.1.1"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/use/download/use-3.1.1.tgz"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utility-types@^3.10.0:
  version "3.10.0"
  resolved "https://registry.npmmirror.com/utility-types/download/utility-types-3.10.0.tgz"
  integrity sha1-6kFI+adBAV8F7XT9YV4dIOa+2Cs=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/utils-merge/download/utils-merge-1.0.1.tgz"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-7.0.3.tgz?cache=0&sync_timestamp=1632753721412&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fuuid%2Fdownload%2Fuuid-7.0.3.tgz"
  integrity "sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs= sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg=="

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

v8-to-istanbul@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmmirror.com/v8-to-istanbul/download/v8-to-istanbul-8.1.0.tgz"
  integrity sha1-Cut2OJTxoKFnat+Ki3YSo4kCRGw=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vlq@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/vlq/download/vlq-1.0.1.tgz"
  integrity "sha1-wAP258C0we3WI/1u5Qu8DWod5Gg= sha512-gQpnTgkubC6hQgdIcRdYGDSDc+SaujOdyesZQMv6JlfQee/9Mp0Qhnys6WxDWvQnL5WZdT7o2Ul187aSt0Rq+w=="

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz?cache=0&sync_timestamp=1632753506598&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fw3c-xmlserializer%2Fdownload%2Fw3c-xmlserializer-2.0.0.tgz"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.8"
  resolved "https://registry.npmmirror.com/walker/download/walker-1.0.8.tgz?cache=0&sync_timestamp=1635238260872&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwalker%2Fdownload%2Fwalker-1.0.8.tgz"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/wcwidth/download/wcwidth-1.0.1.tgz"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-fetch@^3.0.0:
  version "3.6.2"
  resolved "https://registry.npmmirror.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz"
  integrity sha1-3O0k838mJO0CgXJdUdDi4/5nf4w=

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-5.0.0.tgz?cache=0&sync_timestamp=1634673664859&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-5.0.0.tgz"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-8.7.0.tgz"
  integrity sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  integrity sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-module@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/which/download/which-1.3.1.tgz"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/which/download/which-2.0.2.tgz"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.3, word-wrap@~1.2.3:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  integrity "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM= sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^2.3.0:
  version "2.4.3"
  resolved "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz"
  integrity "sha1-H9Lprh3z51uNjDZ0Q8aS1MqB9IE= sha512-GaETH5wwsX+GcnzhPgKcKjJ6M2Cq3/iZp1WyY/X1CSqrW+jVNM9Y7D8EC2sM4ZG/V8wZlSniJnCKWPmBYAucRQ=="
  dependencies:
    graceful-fs "^4.1.11"
    imurmurhash "^0.1.4"
    signal-exit "^3.0.2"

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz?cache=0&sync_timestamp=1632755000530&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-3.0.3.tgz"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@^1.1.0, ws@^1.1.5:
  version "1.1.5"
  resolved "https://registry.npmmirror.com/ws/download/ws-1.1.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-1.1.5.tgz"
  integrity "sha1-y9nm514J/F0skAFfIfDECHXg3VE= sha512-o3KqipXNUdS7wpQzBHSe180lBGO60SoK0yVo3CYJgb2MkobuWuBX6dhkYP5ORCLd55y+SaflMOV5fqAB53ux4w=="
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

ws@^6.1.4:
  version "6.2.2"
  resolved "https://registry.npmmirror.com/ws/download/ws-6.2.2.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-6.2.2.tgz"
  integrity sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4=
  dependencies:
    async-limiter "~1.0.0"

ws@^7, ws@^7.4.6:
  version "7.5.5"
  resolved "https://registry.npmmirror.com/ws/download/ws-7.5.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-7.5.5.tgz"
  integrity sha1-i0vEr1GM+r0Ec65PmRRCh7M+uIE=

xcode@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/xcode/download/xcode-2.1.0.tgz"
  integrity "sha1-urZKfpVLtQyo0Z2n4JUxxlpD7P4= sha512-uCrmPITrqTEzhn0TtT57fJaNaw8YJs1aCzs+P/QqxsDbvPZSv7XMPPwXrKvHtD6pLjBM/NaVwraWJm8q83Y4iQ=="
  dependencies:
    simple-plist "^1.0.0"
    uuid "^3.3.2"

xcode@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/xcode/download/xcode-3.0.1.tgz"
  integrity "sha1-PvtiqsZBqyxwJFj5oDAmlhRqpTw= sha512-kCz5k7J7XbJtjABOvkc5lJmkiDh8VhjVCGNiqdKCscmVpdVUpEAyXv1xmCLkQJ5dsHqx3IPO4XW+NTDhU/fatA=="
  dependencies:
    simple-plist "^1.1.0"
    uuid "^7.0.3"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz?cache=0&sync_timestamp=1632753518490&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fxml-name-validator%2Fdownload%2Fxml-name-validator-3.0.0.tgz"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml2js@^0.4.23:
  version "0.4.23"
  resolved "https://registry.npmmirror.com/xml2js/download/xml2js-0.4.23.tgz"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@^14.0.0:
  version "14.0.0"
  resolved "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-14.0.0.tgz"
  integrity "sha1-h2ta7E8F/9X+uXsKhxyFXRb764w= sha512-ts+B2rSe4fIckR6iquDjsKbQFK2NlUk6iG5nf14mDEyldgoc2nEKZ3jZWMPTxGQwVgToSjt6VGIho1H8/fNFTg=="

xmlbuilder@^9.0.7:
  version "9.0.7"
  resolved "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-9.0.7.tgz"
  integrity sha1-Ey7mPS7FVlxVfiD0wi35rKaGsQ0=

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/xmlchars/download/xmlchars-2.2.0.tgz?cache=0&sync_timestamp=1632753519311&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fxmlchars%2Fdownload%2Fxmlchars-2.2.0.tgz"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xmldoc@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/xmldoc/download/xmldoc-1.1.2.tgz"
  integrity "sha1-ZmbgKf4lRw1ZnNMOI/8NHtUEZtc= sha512-ruPC/fyPNck2BD1dpz0AZZyrEwMOrWTO5lDdIXS91rs3wtm4j+T8Rp2o+zoOYkkAxJTZRPOSnOGei1egoRmKMQ=="
  dependencies:
    sax "^1.2.1"

xtend@~4.0.1:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/y18n/download/y18n-4.0.3.tgz"
  integrity sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmmirror.com/y18n/download/y18n-5.0.8.tgz"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yargs-parser@^18.1.2:
  version "18.1.3"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-18.1.3.tgz?cache=0&sync_timestamp=1632728414140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-18.1.3.tgz"
  integrity "sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A= sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1632728414140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@^15.1.0, yargs@^15.3.1:
  version "15.4.1"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-15.4.1.tgz"
  integrity "sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg= sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.2"

yargs@^16.2.0:
  version "16.2.0"
  resolved "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/yocto-queue/download/yocto-queue-0.1.0.tgz"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
