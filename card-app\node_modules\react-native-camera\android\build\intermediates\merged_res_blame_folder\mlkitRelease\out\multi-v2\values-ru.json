{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\values-ru\\values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5666", "endColumns": "100", "endOffsets": "5762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,5584", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,5661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "215", "endOffsets": "462"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3979", "endColumns": "215", "endOffsets": "4190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,350,548,672,780,981,1108,1249,1378,1618,1723,1917,2041,2267,2451,2546,2632", "endColumns": "104,197,123,107,200,126,140,128,239,104,193,123,225,183,94,85,110", "endOffsets": "349,547,671,779,980,1107,1248,1377,1617,1722,1916,2040,2266,2450,2545,2631,2742"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2822,2931,3129,3257,3369,3570,3701,3846,4195,4435,4544,4738,4866,5092,5280,5379,5469", "endColumns": "108,197,127,111,200,130,144,132,239,108,193,127,225,187,98,89,114", "endOffsets": "2926,3124,3252,3364,3565,3696,3841,3974,4430,4539,4733,4861,5087,5275,5374,5464,5579"}}]}, {"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\incremental\\mergeMlkitReleaseResources\\merged.dir\\values-ru\\values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5666", "endColumns": "100", "endOffsets": "5762"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,5584", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,5661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "215", "endOffsets": "462"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3979", "endColumns": "215", "endOffsets": "4190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,350,548,672,780,981,1108,1249,1378,1618,1723,1917,2041,2267,2451,2546,2632", "endColumns": "104,197,123,107,200,126,140,128,239,104,193,123,225,183,94,85,110", "endOffsets": "349,547,671,779,980,1107,1248,1377,1617,1722,1916,2040,2266,2450,2545,2631,2742"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2822,2931,3129,3257,3369,3570,3701,3846,4195,4435,4544,4738,4866,5092,5280,5379,5469", "endColumns": "108,197,127,111,200,130,144,132,239,108,193,127,225,187,98,89,114", "endOffsets": "2926,3124,3252,3364,3565,3696,3841,3974,4430,4539,4733,4861,5087,5275,5374,5464,5579"}}]}]}