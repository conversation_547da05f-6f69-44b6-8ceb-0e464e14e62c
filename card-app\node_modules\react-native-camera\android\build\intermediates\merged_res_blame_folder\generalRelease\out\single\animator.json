[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_fade_enter.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_fade_enter.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_close_exit.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_close_exit.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_close_enter.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_close_enter.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_open_enter.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_open_enter.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_open_exit.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_open_exit.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_fade_exit.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_fade_exit.xml"}]