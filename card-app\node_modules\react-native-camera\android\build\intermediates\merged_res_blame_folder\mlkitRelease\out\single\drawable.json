[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_paste_mtrl_am_alpha.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_paste_mtrl_am_alpha.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\notification_icon_background.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_icon_background.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_seekbar_track_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_track_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_light_focused.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light_focused.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\notification_bg_low.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_bg_low.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_radio_on_to_off_mtrl_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_default_mtrl_shape.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_default_mtrl_shape.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_light_normal.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light_normal.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_copy_mtrl_am_alpha.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_copy_mtrl_am_alpha.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_dark_focused.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark_focused.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_star_black_48dp.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_star_black_48dp.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_check_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_check_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_disabled.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_disabled.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_tab_indicator_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_tab_indicator_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_cab_background_top_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_cab_background_top_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ratingbar_small_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_small_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\notification_tile_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_tile_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_light_focused.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light_focused.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\notification_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_voice_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_voice_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_cut_mtrl_alpha.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_cut_mtrl_alpha.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\tooltip_frame_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\tooltip_frame_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_share_mtrl_alpha.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_share_mtrl_alpha.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_check_material_anim.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_check_material_anim.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_dark_normal.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark_normal.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_list_selector_background_transition_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\tooltip_frame_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\tooltip_frame_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_radio_on_mtrl.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_on_mtrl.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_radio_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_radio_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ratingbar_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_seekbar_tick_mark_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_tick_mark_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_spinner_textfield_background_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_spinner_textfield_background_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_disabled.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_disabled.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_go_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_go_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_light_normal.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light_normal.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_dark_focused.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark_focused.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_radio_off_to_on_mtrl_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_list_selector_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_vector_test.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07e69b1ced29bd04061c2b8491a4f094\\transformed\\jetified-appcompat-resources-1.4.1\\res\\drawable\\abc_vector_test.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_item_background_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_item_background_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\test_level_drawable.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\test_level_drawable.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_radio_material_anim.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_radio_material_anim.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_ab_back_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_ab_back_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_clear_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_clear_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_list_selector_background_transition_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_background_transition_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_btn_borderless_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_borderless_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_overflow_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_overflow_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_cab_background_internal_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_cab_background_internal_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_text_cursor_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_text_cursor_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ratingbar_indicator_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_indicator_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_icon_dark_normal.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark_normal.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_list_selector_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_ic_menu_selectall_mtrl_alpha.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_selectall_mtrl_alpha.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_checkbox_unchecked_mtrl.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_unchecked_mtrl.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_seekbar_thumb_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_thumb_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_star_half_black_48dp.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_star_half_black_48dp.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_checkbox_checked_mtrl.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_checked_mtrl.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_item_background_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_item_background_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\common_google_signin_btn_text_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_switch_thumb_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_switch_thumb_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\redbox_top_border_background.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\drawable\\redbox_top_border_background.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\btn_radio_off_mtrl.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_off_mtrl.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\drawable\\abc_textfield_search_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_textfield_search_material.xml"}]