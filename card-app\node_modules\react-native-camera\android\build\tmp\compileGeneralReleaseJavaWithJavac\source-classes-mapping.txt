org/reactnative/camera/tasks/TextRecognizerAsyncTask.java
 org.reactnative.camera.tasks.TextRecognizerAsyncTask
org/reactnative/facedetector/RNFaceDetector.java
 org.reactnative.facedetector.RNFaceDetector
org/reactnative/camera/tasks/BarcodeDetectorAsyncTaskDelegate.java
 org.reactnative.camera.tasks.BarcodeDetectorAsyncTaskDelegate
com/google/android/cameraview/Camera2.java
 com.google.android.cameraview.Camera2
 com.google.android.cameraview.Camera2$1
 com.google.android.cameraview.Camera2$2
 com.google.android.cameraview.Camera2$3
 com.google.android.cameraview.Camera2$4
 com.google.android.cameraview.Camera2$5
 com.google.android.cameraview.Camera2$6
 com.google.android.cameraview.Camera2$7
 com.google.android.cameraview.Camera2$8
 com.google.android.cameraview.Camera2$9
 com.google.android.cameraview.Camera2$PictureCaptureCallback
com/lwansbrough/RCTCamera/RCTSensorOrientationChecker.java
 com.lwansbrough.RCTCamera.RCTSensorOrientationChecker
 com.lwansbrough.RCTCamera.RCTSensorOrientationChecker$1
 com.lwansbrough.RCTCamera.RCTSensorOrientationChecker$Listener
 com.lwansbrough.RCTCamera.RCTSensorOrientationListener
org/reactnative/camera/events/BarCodeReadEvent.java
 org.reactnative.camera.events.BarCodeReadEvent
com/google/android/cameraview/CameraView.java
 com.google.android.cameraview.CameraView
 com.google.android.cameraview.CameraView$1
 com.google.android.cameraview.CameraView$Callback
 com.google.android.cameraview.CameraView$CallbackBridge
 com.google.android.cameraview.CameraView$Facing
 com.google.android.cameraview.CameraView$Flash
 com.google.android.cameraview.CameraView$SavedState
 com.google.android.cameraview.CameraView$SavedState$1
org/reactnative/camera/tasks/BarCodeScannerAsyncTask.java
 org.reactnative.camera.tasks.BarCodeScannerAsyncTask
com/google/android/cameraview/PreviewImpl.java
 com.google.android.cameraview.PreviewImpl
 com.google.android.cameraview.PreviewImpl$Callback
com/google/android/cameraview/SizeMap.java
 com.google.android.cameraview.SizeMap
org/reactnative/camera/RNCameraPackage.java
 org.reactnative.camera.RNCameraPackage
org/reactnative/camera/events/TouchEvent.java
 org.reactnative.camera.events.TouchEvent
org/reactnative/camera/Constants.java
 org.reactnative.camera.Constants
com/google/android/cameraview/AspectRatio.java
 com.google.android.cameraview.AspectRatio
 com.google.android.cameraview.AspectRatio$1
org/reactnative/camera/utils/ScopedContext.java
 org.reactnative.camera.utils.ScopedContext
org/reactnative/camera/events/BarcodesDetectedEvent.java
 org.reactnative.camera.events.BarcodesDetectedEvent
org/reactnative/camera/tasks/BarCodeScannerAsyncTaskDelegate.java
 org.reactnative.camera.tasks.BarCodeScannerAsyncTaskDelegate
org/reactnative/barcodedetector/BarcodeFormatUtils.java
 org.reactnative.barcodedetector.BarcodeFormatUtils
com/google/android/cameraview/Constants.java
 com.google.android.cameraview.Constants
com/lwansbrough/RCTCamera/RCTCameraViewFinder.java
 com.lwansbrough.RCTCamera.RCTCameraViewFinder
 com.lwansbrough.RCTCamera.RCTCameraViewFinder$1
 com.lwansbrough.RCTCamera.RCTCameraViewFinder$2
 com.lwansbrough.RCTCamera.RCTCameraViewFinder$ReaderAsyncTask
org/reactnative/camera/RNCameraViewHelper.java
 org.reactnative.camera.RNCameraViewHelper
 org.reactnative.camera.RNCameraViewHelper$1
 org.reactnative.camera.RNCameraViewHelper$10
 org.reactnative.camera.RNCameraViewHelper$11
 org.reactnative.camera.RNCameraViewHelper$12
 org.reactnative.camera.RNCameraViewHelper$13
 org.reactnative.camera.RNCameraViewHelper$2
 org.reactnative.camera.RNCameraViewHelper$3
 org.reactnative.camera.RNCameraViewHelper$4
 org.reactnative.camera.RNCameraViewHelper$5
 org.reactnative.camera.RNCameraViewHelper$6
 org.reactnative.camera.RNCameraViewHelper$7
 org.reactnative.camera.RNCameraViewHelper$8
 org.reactnative.camera.RNCameraViewHelper$9
com/lwansbrough/RCTCamera/RCTCameraModule.java
 com.lwansbrough.RCTCamera.RCTCameraModule
 com.lwansbrough.RCTCamera.RCTCameraModule$1
 com.lwansbrough.RCTCamera.RCTCameraModule$1$1
 com.lwansbrough.RCTCamera.RCTCameraModule$1$2
 com.lwansbrough.RCTCamera.RCTCameraModule$1$3
 com.lwansbrough.RCTCamera.RCTCameraModule$1$4
 com.lwansbrough.RCTCamera.RCTCameraModule$1$5
 com.lwansbrough.RCTCamera.RCTCameraModule$1$6
 com.lwansbrough.RCTCamera.RCTCameraModule$1$7
 com.lwansbrough.RCTCamera.RCTCameraModule$1$8
 com.lwansbrough.RCTCamera.RCTCameraModule$1$9
 com.lwansbrough.RCTCamera.RCTCameraModule$2
 com.lwansbrough.RCTCamera.RCTCameraModule$3
 com.lwansbrough.RCTCamera.RCTCameraModule$3$1
 com.lwansbrough.RCTCamera.RCTCameraModule$4
 com.lwansbrough.RCTCamera.RCTCameraModule$5
org/reactnative/facedetector/tasks/FileFaceDetectionAsyncTask.java
 org.reactnative.facedetector.tasks.FileFaceDetectionAsyncTask
org/reactnative/camera/events/TextRecognizedEvent.java
 org.reactnative.camera.events.TextRecognizedEvent
org/reactnative/camera/events/RecordingStartEvent.java
 org.reactnative.camera.events.RecordingStartEvent
org/reactnative/camera/events/CameraReadyEvent.java
 org.reactnative.camera.events.CameraReadyEvent
org/reactnative/barcodedetector/RNBarcodeDetector.java
 org.reactnative.barcodedetector.RNBarcodeDetector
org/reactnative/camera/events/PictureTakenEvent.java
 org.reactnative.camera.events.PictureTakenEvent
org/reactnative/camera/events/RecordingEndEvent.java
 org.reactnative.camera.events.RecordingEndEvent
org/reactnative/camera/events/PictureSavedEvent.java
 org.reactnative.camera.events.PictureSavedEvent
com/lwansbrough/RCTCamera/RCTCameraView.java
 com.lwansbrough.RCTCamera.RCTCameraView
 com.lwansbrough.RCTCamera.RCTCameraView$1
org/reactnative/camera/tasks/FaceDetectorAsyncTaskDelegate.java
 org.reactnative.camera.tasks.FaceDetectorAsyncTaskDelegate
org/reactnative/camera/BuildConfig.java
 org.reactnative.camera.BuildConfig
org/reactnative/facedetector/FaceDetectorUtils.java
 org.reactnative.facedetector.FaceDetectorUtils
org/reactnative/camera/tasks/TextRecognizerAsyncTaskDelegate.java
 org.reactnative.camera.tasks.TextRecognizerAsyncTaskDelegate
org/reactnative/camera/CameraViewManager.java
 org.reactnative.camera.CameraViewManager
 org.reactnative.camera.CameraViewManager$Events
com/lwansbrough/RCTCamera/RCTCamera.java
 com.lwansbrough.RCTCamera.RCTCamera
 com.lwansbrough.RCTCamera.RCTCamera$CameraInfoWrapper
 com.lwansbrough.RCTCamera.RCTCamera$Resolution
com/lwansbrough/RCTCamera/MutableImage.java
 com.lwansbrough.RCTCamera.MutableImage
 com.lwansbrough.RCTCamera.MutableImage$GPS
 com.lwansbrough.RCTCamera.MutableImage$ImageMutationFailedException
org/reactnative/camera/utils/RNFileUtils.java
 org.reactnative.camera.utils.RNFileUtils
com/google/android/cameraview/DisplayOrientationDetector.java
 com.google.android.cameraview.DisplayOrientationDetector
 com.google.android.cameraview.DisplayOrientationDetector$1
com/google/android/cameraview/SurfaceViewPreview.java
 com.google.android.cameraview.SurfaceViewPreview
 com.google.android.cameraview.SurfaceViewPreview$1
org/reactnative/camera/tasks/PictureSavedDelegate.java
 org.reactnative.camera.tasks.PictureSavedDelegate
org/reactnative/facedetector/FaceDetectorModule.java
 org.reactnative.facedetector.FaceDetectorModule
 org.reactnative.facedetector.FaceDetectorModule$1
 org.reactnative.facedetector.FaceDetectorModule$1$1
 org.reactnative.facedetector.FaceDetectorModule$1$2
 org.reactnative.facedetector.FaceDetectorModule$1$3
com/google/android/cameraview/CameraViewImpl.java
 com.google.android.cameraview.CameraViewImpl
 com.google.android.cameraview.CameraViewImpl$Callback
com/lwansbrough/RCTCamera/RCTCameraViewManager.java
 com.lwansbrough.RCTCamera.RCTCameraViewManager
org/reactnative/camera/utils/ObjectUtils.java
 org.reactnative.camera.utils.ObjectUtils
com/lwansbrough/RCTCamera/RCTCameraUtils.java
 com.lwansbrough.RCTCamera.RCTCameraUtils
com/google/android/cameraview/TextureViewPreview.java
 com.google.android.cameraview.TextureViewPreview
 com.google.android.cameraview.TextureViewPreview$1
com/google/android/cameraview/Camera2Api23.java
 com.google.android.cameraview.Camera2Api23
org/reactnative/camera/tasks/ResolveTakenPictureAsyncTask.java
 org.reactnative.camera.tasks.ResolveTakenPictureAsyncTask
 org.reactnative.camera.tasks.ResolveTakenPictureAsyncTask$1
org/reactnative/camera/CameraModule.java
 org.reactnative.camera.CameraModule
 org.reactnative.camera.CameraModule$1
 org.reactnative.camera.CameraModule$10
 org.reactnative.camera.CameraModule$11
 org.reactnative.camera.CameraModule$12
 org.reactnative.camera.CameraModule$13
 org.reactnative.camera.CameraModule$14
 org.reactnative.camera.CameraModule$2
 org.reactnative.camera.CameraModule$2$1
 org.reactnative.camera.CameraModule$2$1$1
 org.reactnative.camera.CameraModule$2$1$2
 org.reactnative.camera.CameraModule$2$1$3
 org.reactnative.camera.CameraModule$2$2
 org.reactnative.camera.CameraModule$2$3
 org.reactnative.camera.CameraModule$2$4
 org.reactnative.camera.CameraModule$2$5
 org.reactnative.camera.CameraModule$2$6
 org.reactnative.camera.CameraModule$2$7
 org.reactnative.camera.CameraModule$2$8
 org.reactnative.camera.CameraModule$2$9
 org.reactnative.camera.CameraModule$3
 org.reactnative.camera.CameraModule$4
 org.reactnative.camera.CameraModule$5
 org.reactnative.camera.CameraModule$6
 org.reactnative.camera.CameraModule$7
 org.reactnative.camera.CameraModule$8
 org.reactnative.camera.CameraModule$9
org/reactnative/camera/events/FaceDetectionErrorEvent.java
 org.reactnative.camera.events.FaceDetectionErrorEvent
org/reactnative/camera/tasks/FaceDetectorAsyncTask.java
 org.reactnative.camera.tasks.FaceDetectorAsyncTask
org/reactnative/camera/utils/ImageDimensions.java
 org.reactnative.camera.utils.ImageDimensions
com/google/android/cameraview/Size.java
 com.google.android.cameraview.Size
 com.google.android.cameraview.Size$1
org/reactnative/frame/RNFrame.java
 org.reactnative.frame.RNFrame
org/reactnative/camera/events/FacesDetectedEvent.java
 org.reactnative.camera.events.FacesDetectedEvent
org/reactnative/frame/RNFrameFactory.java
 org.reactnative.frame.RNFrameFactory
org/reactnative/camera/events/CameraMountErrorEvent.java
 org.reactnative.camera.events.CameraMountErrorEvent
org/reactnative/camera/RNCameraView.java
 org.reactnative.camera.RNCameraView
 org.reactnative.camera.RNCameraView$1
 org.reactnative.camera.RNCameraView$2
 org.reactnative.camera.RNCameraView$3
 org.reactnative.camera.RNCameraView$4
 org.reactnative.camera.RNCameraView$5
 org.reactnative.camera.RNCameraView$6
 org.reactnative.camera.RNCameraView$7
com/google/android/cameraview/Camera1.java
 com.google.android.cameraview.Camera1
 com.google.android.cameraview.Camera1$1
 com.google.android.cameraview.Camera1$1$1
 com.google.android.cameraview.Camera1$1$2
 com.google.android.cameraview.Camera1$10
 com.google.android.cameraview.Camera1$10$1
 com.google.android.cameraview.Camera1$10$2
 com.google.android.cameraview.Camera1$10$3
 com.google.android.cameraview.Camera1$11
 com.google.android.cameraview.Camera1$2
 com.google.android.cameraview.Camera1$3
 com.google.android.cameraview.Camera1$4
 com.google.android.cameraview.Camera1$5
 com.google.android.cameraview.Camera1$6
 com.google.android.cameraview.Camera1$7
 com.google.android.cameraview.Camera1$8
 com.google.android.cameraview.Camera1$9
org/reactnative/camera/tasks/BarcodeDetectorAsyncTask.java
 org.reactnative.camera.tasks.BarcodeDetectorAsyncTask
org/reactnative/camera/events/BarcodeDetectionErrorEvent.java
 org.reactnative.camera.events.BarcodeDetectionErrorEvent
