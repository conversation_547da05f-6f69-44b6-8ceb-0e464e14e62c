[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_activity_chooser_view_list_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_activity_chooser_view_list_item.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_expanded_menu_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_expanded_menu_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\custom_dialog.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\custom_dialog.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\autofill_inline_suggestion.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\be9076cd4fc3ff6ad98aa6fb277b3148\\transformed\\jetified-autofill-1.1.0\\res\\layout\\autofill_inline_suggestion.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\select_dialog_item_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_item_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_screen_content_include.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_content_include.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_search_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_search_view.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_menu_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_menu_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_tooltip.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_tooltip.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\redbox_item_frame.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_item_frame.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\support_simple_spinner_dropdown_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\support_simple_spinner_dropdown_item.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_popup_menu_header_item_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_popup_menu_header_item_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_select_dialog_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_select_dialog_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_mode_bar.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_mode_bar.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_list_menu_item_checkbox.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_checkbox.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_bar_title_item.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_bar_title_item.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\fps_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\fps_view.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_screen_toolbar.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_toolbar.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_cascading_menu_item_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_cascading_menu_item_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_alert_dialog_button_bar_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_button_bar_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_alert_dialog_title_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_title_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_search_dropdown_item_icons_2line.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_search_dropdown_item_icons_2line.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_menu_item_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_menu_item_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_alert_dialog_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\select_dialog_multichoice_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_multichoice_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_list_menu_item_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\select_dialog_singlechoice_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_singlechoice_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_dialog_title_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_dialog_title_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_screen_simple.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_simple.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_list_menu_item_icon.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_icon.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_list_menu_item_radio.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_radio.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\dev_loading_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\dev_loading_view.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\redbox_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_view.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_activity_chooser_view.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_activity_chooser_view.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\notification_template_part_time.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\notification_template_part_time.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_popup_menu_item_layout.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_popup_menu_item_layout.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_bar_up_container.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_bar_up_container.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_screen_simple_overlay_action_mode.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_simple_overlay_action_mode.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\redbox_item_title.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_item_title.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\abc_action_mode_close_item_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_mode_close_item_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\layout\\notification_template_part_chronometer.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\notification_template_part_chronometer.xml"}]