<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2020 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="?attr/autofillInlineSuggestionChip"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/autofill_inline_suggestion_start_icon"
        style="?attr/autofillInlineSuggestionStartIconStyle"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/autofill_inline_suggestion_title"
        style="?attr/autofillInlineSuggestionTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/autofill_inline_suggestion_subtitle"
        style="?attr/autofillInlineSuggestionSubtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/autofill_inline_suggestion_end_icon"
        style="?attr/autofillInlineSuggestionEndIconStyle"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:visibility="gone"/>
</LinearLayout>
