<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<inset xmlns:android="http://schemas.android.com/apk/res/android"
       android:insetLeft="@dimen/abc_edit_text_inset_horizontal_material"
       android:insetRight="@dimen/abc_edit_text_inset_horizontal_material"
       android:insetTop="@dimen/abc_edit_text_inset_top_material"
       android:insetBottom="@dimen/abc_edit_text_inset_bottom_material">
    <selector>
        <item android:state_enabled="false">
            <nine-patch android:src="@drawable/abc_textfield_default_mtrl_alpha"
                        android:tint="?attr/colorControlNormal"
                        android:alpha="?android:attr/disabledAlpha"/>
        </item>
        <item android:state_pressed="false" android:state_focused="false">
            <nine-patch android:src="@drawable/abc_textfield_default_mtrl_alpha"
                        android:tint="?attr/colorControlNormal"/>
        </item>
        <item>
            <nine-patch android:src="@drawable/abc_textfield_activated_mtrl_alpha"
                        android:tint="?attr/colorControlActivated"/>
        </item>
    </selector>
</inset>