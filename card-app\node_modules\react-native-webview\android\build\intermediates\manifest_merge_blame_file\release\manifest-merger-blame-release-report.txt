1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.reactnativecommunity.webview" >
4
5    <uses-sdk
6        android:minSdkVersion="21"
6-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
7        android:targetSdkVersion="30" />
7-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
8
9    <application>
9-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:4:3-14:17
10        <provider
10-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:5:5-13:16
11            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
11-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:6:7-45
12            android:authorities="${applicationId}.fileprovider"
12-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:7:7-58
13            android:exported="false"
13-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:8:7-31
14            android:grantUriPermissions="true" >
14-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:9:7-41
15            <meta-data
15-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:10:7-12:55
16                android:name="android.support.FILE_PROVIDER_PATHS"
16-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:11:9-59
17                android:resource="@xml/file_provider_paths" />
17-->D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:12:9-52
18        </provider>
19    </application>
20
21</manifest>
