-- Merging decision tree log ---
manifest
ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:1:1-15:12
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:1:1-15:12
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:1:1-15:12
	package
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:2:3-45
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:1:11-69
application
ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:4:3-14:17
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:5:5-13:16
	android:grantUriPermissions
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:9:7-41
	android:authorities
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:7:7-58
	android:exported
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:8:7-31
	android:name
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:6:7-45
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:10:7-12:55
	android:resource
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:12:9-52
	android:name
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml:11:9-59
uses-sdk
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
		ADDED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
		INJECTED from D:\Sites\card-all\card-app\node_modules\react-native-webview\android\src\main\AndroidManifest.xml
