[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_action_tombstone.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_action_tombstone.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_template_custom_big.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_template_custom_big.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_action.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_action.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_template_icon_group.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_template_icon_group.xml"}]