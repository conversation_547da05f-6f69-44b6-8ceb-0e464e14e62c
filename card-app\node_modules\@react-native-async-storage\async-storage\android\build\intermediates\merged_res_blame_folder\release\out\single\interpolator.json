[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\fast_out_slow_in.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\fast_out_slow_in.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\intermediates\\res\\merged\\release\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml"}]