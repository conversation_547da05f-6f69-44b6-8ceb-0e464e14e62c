{"systemParams": "win32-x64-93", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@ant-design/icons-react-native@^2.3.2", "@ant-design/react-native@^4.2.0", "@babel/core@^7.16.0", "@babel/runtime@^7.16.0", "@react-native-async-storage/async-storage@^1.15.11", "@react-native-community/cameraroll@^4.1.2", "@react-native-community/eslint-config@^3.0.1", "@react-native-community/masked-view@^0.1.11", "@react-native-community/segmented-control@^2.2.2", "@react-native-community/slider@^4.1.11", "@react-native-picker/picker@^2.2.0", "babel-jest@^27.3.1", "babel-plugin-import@^1.13.3", "babel-plugin-module-resolver@^4.1.0", "eslint@^8.2.0", "jest@^27.3.1", "metro-react-native-babel-preset@^0.66.2", "moment@^2.29.1", "ms@^2.1.3", "react-native-camera@^4.2.1", "react-native-gesture-handler@^1.10.3", "react-native-nfc-manager@^3.11.0", "react-native-pager-view@^5.4.8", "react-native-safe-area-context@^3.3.2", "react-native-webview@^11.14.2", "react-native@0.66.2", "react-navigation-stack@^2.10.4", "react-navigation@^4.4.4", "react-test-renderer@17.0.2", "react@17.0.2"], "lockfileEntries": {"@ant-design/icons-react-native@^2.3.1": "https://registry.npmmirror.com/@ant-design/icons-react-native/download/@ant-design/icons-react-native-2.3.2.tgz", "@ant-design/icons-react-native@^2.3.2": "https://registry.npmmirror.com/@ant-design/icons-react-native/download/@ant-design/icons-react-native-2.3.2.tgz", "@ant-design/react-native@^4.2.0": "https://registry.npmmirror.com/@ant-design/react-native/download/@ant-design/react-native-4.2.0.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.16.0": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@^7.18.6": "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/code-frame@~7.10.4": "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.10.4.tgz?cache=0&sync_timestamp=1635561060995&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.10.4.tgz", "@babel/compat-data@^7.13.11": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.20.1.tgz", "@babel/compat-data@^7.20.0": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.20.1.tgz", "@babel/compat-data@^7.20.1": "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.20.1.tgz", "@babel/core@^7.1.0": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.1.6": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.12.3": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.14.0": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.16.0": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.7.2": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/core@^7.7.5": "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635560662864&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz", "@babel/generator@^7.14.0": "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.4.tgz", "@babel/generator@^7.16.0": "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.4.tgz", "@babel/generator@^7.20.1": "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.4.tgz", "@babel/generator@^7.7.2": "https://registry.npmmirror.com/@babel/generator/-/generator-7.20.4.tgz", "@babel/helper-annotate-as-pure@^7.16.0": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "@babel/helper-annotate-as-pure@^7.18.6": "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6": "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz", "@babel/helper-compilation-targets@^7.13.0": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "@babel/helper-compilation-targets@^7.16.0": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "@babel/helper-compilation-targets@^7.18.9": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "@babel/helper-compilation-targets@^7.20.0": "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "@babel/helper-create-class-features-plugin@^7.16.0": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.2.tgz", "@babel/helper-create-class-features-plugin@^7.18.6": "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.20.2.tgz", "@babel/helper-create-regexp-features-plugin@^7.18.6": "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.19.0.tgz", "@babel/helper-define-polyfill-provider@^0.2.4": "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.2.4.tgz", "@babel/helper-environment-visitor@^7.18.9": "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "@babel/helper-explode-assignable-expression@^7.18.6": "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/-/helper-explode-assignable-expression-7.18.6.tgz", "@babel/helper-function-name@^7.18.9": "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "@babel/helper-function-name@^7.19.0": "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "@babel/helper-hoist-variables@^7.18.6": "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "@babel/helper-member-expression-to-functions@^7.18.9": "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.18.9.tgz", "@babel/helper-module-imports@^7.0.0": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-imports@^7.12.13": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-imports@^7.16.0": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-imports@^7.18.6": "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-transforms@^7.16.0": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz", "@babel/helper-module-transforms@^7.19.6": "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz", "@babel/helper-optimise-call-expression@^7.18.6": "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.18.6.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.13.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.18.9": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.19.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.20.2": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "@babel/helper-remap-async-to-generator@^7.18.6": "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.18.9.tgz", "@babel/helper-replace-supers@^7.18.6": "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz", "@babel/helper-replace-supers@^7.19.1": "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.19.1.tgz", "@babel/helper-simple-access@^7.19.4": "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "@babel/helper-simple-access@^7.20.2": "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.18.9": "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.20.0.tgz", "@babel/helper-split-export-declaration@^7.18.6": "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "@babel/helper-string-parser@^7.19.4": "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "@babel/helper-validator-identifier@^7.18.6": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-identifier@^7.19.1": "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-option@^7.14.5": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "@babel/helper-validator-option@^7.18.6": "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "@babel/helper-wrap-function@^7.18.9": "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.19.0.tgz", "@babel/helpers@^7.16.0": "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.16.0.tgz?cache=0&sync_timestamp=1635560664381&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelpers%2Fdownload%2F%40babel%2Fhelpers-7.16.0.tgz", "@babel/highlight@^7.10.4": "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/highlight@^7.18.6": "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/parser@^7.1.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.1.6": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.14.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.14.7": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.16.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.18.10": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.20.1": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.7.0": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/parser@^7.7.2": "https://registry.npmmirror.com/@babel/parser/-/parser-7.20.3.tgz", "@babel/plugin-proposal-class-properties@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-class-properties@^7.1.0": "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-export-default-from@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-proposal-export-default-from%2Fdownload%2F%40babel%2Fplugin-proposal-export-default-from-7.16.0.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.1.0": "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-object-rest-spread@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.2.tgz", "@babel/plugin-proposal-optional-catch-binding@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz", "@babel/plugin-proposal-optional-chaining@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz", "@babel/plugin-proposal-optional-chaining@^7.1.0": "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.18.9.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-properties@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-dynamic-import@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-export-default-from@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-export-default-from%2Fdownload%2F%40babel%2Fplugin-syntax-export-default-from-7.16.0.tgz", "@babel/plugin-syntax-export-default-from@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-export-default-from%2Fdownload%2F%40babel%2Fplugin-syntax-export-default-from-7.16.0.tgz", "@babel/plugin-syntax-flow@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.16.0.tgz?cache=0&sync_timestamp=1635740663618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-flow%2Fdownload%2F%40babel%2Fplugin-syntax-flow-7.16.0.tgz", "@babel/plugin-syntax-flow@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.16.0.tgz?cache=0&sync_timestamp=1635740663618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-flow%2Fdownload%2F%40babel%2Fplugin-syntax-flow-7.16.0.tgz", "@babel/plugin-syntax-flow@^7.2.0": "https://registry.npmmirror.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.16.0.tgz?cache=0&sync_timestamp=1635740663618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-flow%2Fdownload%2F%40babel%2Fplugin-syntax-flow-7.16.0.tgz", "@babel/plugin-syntax-import-meta@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.16.0.tgz", "@babel/plugin-syntax-jsx@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-jsx%2Fdownload%2F%40babel%2Fplugin-syntax-jsx-7.16.0.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-top-level-await@^7.8.3": "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.16.0.tgz?cache=0&sync_timestamp=1635560844194&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-typescript%2Fdownload%2F%40babel%2Fplugin-syntax-typescript-7.16.0.tgz", "@babel/plugin-syntax-typescript@^7.7.2": "https://registry.npmmirror.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.16.0.tgz?cache=0&sync_timestamp=1635560844194&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-typescript%2Fdownload%2F%40babel%2Fplugin-syntax-typescript-7.16.0.tgz", "@babel/plugin-transform-arrow-functions@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.18.6.tgz", "@babel/plugin-transform-async-to-generator@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.18.6.tgz", "@babel/plugin-transform-block-scoped-functions@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.18.6.tgz", "@babel/plugin-transform-block-scoping@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.20.2.tgz", "@babel/plugin-transform-classes@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.20.2.tgz", "@babel/plugin-transform-computed-properties@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.18.9.tgz", "@babel/plugin-transform-destructuring@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.20.2.tgz", "@babel/plugin-transform-exponentiation-operator@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.18.6.tgz", "@babel/plugin-transform-flow-strip-types@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.16.0.tgz?cache=0&sync_timestamp=1635740663071&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-flow-strip-types%2Fdownload%2F%40babel%2Fplugin-transform-flow-strip-types-7.16.0.tgz", "@babel/plugin-transform-flow-strip-types@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.16.0.tgz?cache=0&sync_timestamp=1635740663071&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-flow-strip-types%2Fdownload%2F%40babel%2Fplugin-transform-flow-strip-types-7.16.0.tgz", "@babel/plugin-transform-for-of@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.18.8.tgz", "@babel/plugin-transform-function-name@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.18.9.tgz", "@babel/plugin-transform-literals@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.18.9.tgz", "@babel/plugin-transform-member-expression-literals@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.18.6.tgz", "@babel/plugin-transform-modules-commonjs@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.19.6.tgz", "@babel/plugin-transform-modules-commonjs@^7.1.0": "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.19.6.tgz", "@babel/plugin-transform-object-assign@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-object-assign/download/@babel/plugin-transform-object-assign-7.16.0.tgz", "@babel/plugin-transform-object-super@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.18.6.tgz", "@babel/plugin-transform-parameters@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.3.tgz", "@babel/plugin-transform-parameters@^7.20.1": "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.20.3.tgz", "@babel/plugin-transform-property-literals@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.18.6.tgz", "@babel/plugin-transform-react-display-name@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.16.0.tgz", "@babel/plugin-transform-react-jsx-self@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.16.0.tgz", "@babel/plugin-transform-react-jsx-source@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.16.0.tgz", "@babel/plugin-transform-react-jsx@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.16.0.tgz", "@babel/plugin-transform-regenerator@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.18.6.tgz", "@babel/plugin-transform-runtime@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-runtime%2Fdownload%2F%40babel%2Fplugin-transform-runtime-7.16.0.tgz", "@babel/plugin-transform-shorthand-properties@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.18.6.tgz", "@babel/plugin-transform-spread@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.19.0.tgz", "@babel/plugin-transform-sticky-regex@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.18.6.tgz", "@babel/plugin-transform-template-literals@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.18.9.tgz", "@babel/plugin-transform-typescript@^7.16.0": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.16.1.tgz?cache=0&sync_timestamp=1635663706468&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-typescript%2Fdownload%2F%40babel%2Fplugin-transform-typescript-7.16.1.tgz", "@babel/plugin-transform-typescript@^7.5.0": "https://registry.npmmirror.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.16.1.tgz?cache=0&sync_timestamp=1635663706468&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-typescript%2Fdownload%2F%40babel%2Fplugin-transform-typescript-7.16.1.tgz", "@babel/plugin-transform-unicode-regex@^7.0.0": "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.18.6.tgz", "@babel/preset-flow@^7.0.0": "https://registry.npmmirror.com/@babel/preset-flow/download/@babel/preset-flow-7.16.0.tgz", "@babel/preset-typescript@^7.1.0": "https://registry.npmmirror.com/@babel/preset-typescript/download/@babel/preset-typescript-7.16.0.tgz?cache=0&sync_timestamp=1635579061274&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fpreset-typescript%2Fdownload%2F%40babel%2Fpreset-typescript-7.16.0.tgz", "@babel/register@^7.0.0": "https://registry.npmmirror.com/@babel/register/download/@babel/register-7.16.0.tgz?cache=0&sync_timestamp=1635737101826&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fregister%2Fdownload%2F%40babel%2Fregister-7.16.0.tgz", "@babel/runtime@^7.0.0": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.0.tgz?cache=0&sync_timestamp=1635554597219&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.16.0.tgz", "@babel/runtime@^7.16.0": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.0.tgz?cache=0&sync_timestamp=1635554597219&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.16.0.tgz", "@babel/runtime@^7.8.4": "https://registry.npmmirror.com/@babel/runtime/download/@babel/runtime-7.16.0.tgz?cache=0&sync_timestamp=1635554597219&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fruntime%2Fdownload%2F%40babel%2Fruntime-7.16.0.tgz", "@babel/template@^7.0.0": "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz", "@babel/template@^7.16.0": "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz", "@babel/template@^7.18.10": "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz", "@babel/template@^7.3.3": "https://registry.npmmirror.com/@babel/template/-/template-7.18.10.tgz", "@babel/traverse@^7.1.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.13.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.14.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.16.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.19.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.19.1": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.20.1": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.7.0": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.7.2": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/traverse@^7.7.4": "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.20.1.tgz", "@babel/types@^7.0.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.16.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.18.10": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.18.6": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.18.9": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.19.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.20.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.20.2": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.3.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.3.3": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@babel/types@^7.7.0": "https://registry.npmmirror.com/@babel/types/-/types-7.20.2.tgz", "@bang88/react-native-ultimate-listview@^4.0.0": "https://registry.npmmirror.com/@bang88/react-native-ultimate-listview/download/@bang88/react-native-ultimate-listview-4.0.0.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmmirror.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz", "@cnakazawa/watch@^1.0.3": "https://registry.npmmirror.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz", "@egjs/hammerjs@^2.0.17": "https://registry.npmmirror.com/@egjs/hammerjs/download/@egjs/hammerjs-2.0.17.tgz", "@eslint/eslintrc@^1.0.4": "https://registry.npmmirror.com/@eslint/eslintrc/download/@eslint/eslintrc-1.0.4.tgz", "@expo/config-plugins@^3.0.6": "https://registry.npmmirror.com/@expo/config-plugins/download/@expo/config-plugins-3.1.0.tgz", "@expo/config-types@^42.0.0": "https://registry.npmmirror.com/@expo/config-types/download/@expo/config-types-42.0.0.tgz", "@expo/json-file@8.2.33": "https://registry.npmmirror.com/@expo/json-file/download/@expo/json-file-8.2.33.tgz", "@expo/plist@0.0.14": "https://registry.npmmirror.com/@expo/plist/download/@expo/plist-0.0.14.tgz", "@hapi/hoek@^9.0.0": "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-9.2.1.tgz", "@hapi/topo@^5.0.0": "https://registry.npmmirror.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz", "@humanwhocodes/config-array@^0.6.0": "https://registry.npmmirror.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.6.0.tgz", "@humanwhocodes/object-schema@^1.2.0": "https://registry.npmmirror.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmmirror.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmmirror.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz", "@jest/console@^27.3.1": "https://registry.npmmirror.com/@jest/console/download/@jest/console-27.3.1.tgz?cache=0&sync_timestamp=1634627428059&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fconsole%2Fdownload%2F%40jest%2Fconsole-27.3.1.tgz", "@jest/core@^27.3.1": "https://registry.npmmirror.com/@jest/core/download/@jest/core-27.3.1.tgz", "@jest/create-cache-key-function@^27.0.1": "https://registry.npmmirror.com/@jest/create-cache-key-function/download/@jest/create-cache-key-function-27.3.1.tgz?cache=0&sync_timestamp=1634627761890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fcreate-cache-key-function%2Fdownload%2F%40jest%2Fcreate-cache-key-function-27.3.1.tgz", "@jest/environment@^27.3.1": "https://registry.npmmirror.com/@jest/environment/download/@jest/environment-27.3.1.tgz?cache=0&sync_timestamp=1634627425622&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Fenvironment%2Fdownload%2F%40jest%2Fenvironment-27.3.1.tgz", "@jest/fake-timers@^27.3.1": "https://registry.npmmirror.com/@jest/fake-timers/download/@jest/fake-timers-27.3.1.tgz?cache=0&sync_timestamp=1634627446944&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Ffake-timers%2Fdownload%2F%40jest%2Ffake-timers-27.3.1.tgz", "@jest/globals@^27.3.1": "https://registry.npmmirror.com/@jest/globals/download/@jest/globals-27.3.1.tgz", "@jest/reporters@^27.3.1": "https://registry.npmmirror.com/@jest/reporters/download/@jest/reporters-27.3.1.tgz", "@jest/source-map@^27.0.6": "https://registry.npmmirror.com/@jest/source-map/download/@jest/source-map-27.0.6.tgz", "@jest/test-result@^27.3.1": "https://registry.npmmirror.com/@jest/test-result/download/@jest/test-result-27.3.1.tgz?cache=0&sync_timestamp=1634627424292&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40jest%2Ftest-result%2Fdownload%2F%40jest%2Ftest-result-27.3.1.tgz", "@jest/test-sequencer@^27.3.1": "https://registry.npmmirror.com/@jest/test-sequencer/download/@jest/test-sequencer-27.3.1.tgz", "@jest/transform@^27.3.1": "https://registry.npmmirror.com/@jest/transform/download/@jest/transform-27.3.1.tgz", "@jest/types@^26.6.2": "https://registry.npmmirror.com/@jest/types/download/@jest/types-26.6.2.tgz", "@jest/types@^27.2.5": "https://registry.npmmirror.com/@jest/types/download/@jest/types-27.2.5.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "@jridgewell/resolve-uri@3.1.0": "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "@jridgewell/set-array@^1.0.1": "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/sourcemap-codec@1.4.14": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmmirror.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmmirror.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz", "@react-native-async-storage/async-storage@^1.15.11": "https://registry.npmmirror.com/@react-native-async-storage/async-storage/download/@react-native-async-storage/async-storage-1.15.11.tgz", "@react-native-community/cameraroll@^4.1.2": "https://registry.npmmirror.com/@react-native-community/cameraroll/download/@react-native-community/cameraroll-4.1.2.tgz", "@react-native-community/cli-debugger-ui@^6.0.0-rc.0": "https://registry.npmmirror.com/@react-native-community/cli-debugger-ui/download/@react-native-community/cli-debugger-ui-6.0.0-rc.0.tgz", "@react-native-community/cli-hermes@^6.1.0": "https://registry.npmmirror.com/@react-native-community/cli-hermes/download/@react-native-community/cli-hermes-6.1.0.tgz?cache=0&sync_timestamp=1633349772330&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-hermes%2Fdownload%2F%40react-native-community%2Fcli-hermes-6.1.0.tgz", "@react-native-community/cli-platform-android@^6.0.0": "https://registry.npmmirror.com/@react-native-community/cli-platform-android/download/@react-native-community/cli-platform-android-6.1.0.tgz?cache=0&sync_timestamp=1633349764886&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-platform-android%2Fdownload%2F%40react-native-community%2Fcli-platform-android-6.1.0.tgz", "@react-native-community/cli-platform-android@^6.1.0": "https://registry.npmmirror.com/@react-native-community/cli-platform-android/download/@react-native-community/cli-platform-android-6.1.0.tgz?cache=0&sync_timestamp=1633349764886&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-platform-android%2Fdownload%2F%40react-native-community%2Fcli-platform-android-6.1.0.tgz", "@react-native-community/cli-platform-ios@^6.0.0": "https://registry.npmmirror.com/@react-native-community/cli-platform-ios/download/@react-native-community/cli-platform-ios-6.1.0.tgz?cache=0&sync_timestamp=1633349770978&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-platform-ios%2Fdownload%2F%40react-native-community%2Fcli-platform-ios-6.1.0.tgz", "@react-native-community/cli-plugin-metro@^6.1.0": "https://registry.npmmirror.com/@react-native-community/cli-plugin-metro/download/@react-native-community/cli-plugin-metro-6.1.0.tgz", "@react-native-community/cli-server-api@^6.1.0": "https://registry.npmmirror.com/@react-native-community/cli-server-api/download/@react-native-community/cli-server-api-6.1.0.tgz?cache=0&sync_timestamp=1633349771866&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-server-api%2Fdownload%2F%40react-native-community%2Fcli-server-api-6.1.0.tgz", "@react-native-community/cli-tools@^6.1.0": "https://registry.npmmirror.com/@react-native-community/cli-tools/download/@react-native-community/cli-tools-6.1.0.tgz?cache=0&sync_timestamp=1633349770140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli-tools%2Fdownload%2F%40react-native-community%2Fcli-tools-6.1.0.tgz", "@react-native-community/cli-types@^6.0.0": "https://registry.npmmirror.com/@react-native-community/cli-types/download/@react-native-community/cli-types-6.0.0.tgz", "@react-native-community/cli@^6.0.0": "https://registry.npmmirror.com/@react-native-community/cli/download/@react-native-community/cli-6.1.0.tgz?cache=0&sync_timestamp=1633349765333&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40react-native-community%2Fcli%2Fdownload%2F%40react-native-community%2Fcli-6.1.0.tgz", "@react-native-community/eslint-config@^3.0.1": "https://registry.npmmirror.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-3.0.1.tgz", "@react-native-community/eslint-plugin@^1.1.0": "https://registry.npmmirror.com/@react-native-community/eslint-plugin/download/@react-native-community/eslint-plugin-1.1.0.tgz", "@react-native-community/masked-view@^0.1.11": "https://registry.npmmirror.com/@react-native-community/masked-view/download/@react-native-community/masked-view-0.1.11.tgz", "@react-native-community/segmented-control@^2.2.2": "https://registry.npmmirror.com/@react-native-community/segmented-control/download/@react-native-community/segmented-control-2.2.2.tgz", "@react-native-community/slider@^4.1.11": "https://registry.npmmirror.com/@react-native-community/slider/download/@react-native-community/slider-4.1.11.tgz", "@react-native-picker/picker@^2.2.0": "https://registry.npmmirror.com/@react-native-picker/picker/download/@react-native-picker/picker-2.2.0.tgz", "@react-native/assets@1.0.0": "https://registry.npmmirror.com/@react-native/assets/download/@react-native/assets-1.0.0.tgz", "@react-native/normalize-color@1.0.0": "https://registry.npmmirror.com/@react-native/normalize-color/download/@react-native/normalize-color-1.0.0.tgz", "@react-native/polyfills@2.0.0": "https://registry.npmmirror.com/@react-native/polyfills/download/@react-native/polyfills-2.0.0.tgz", "@react-navigation/core@^3.7.9": "https://registry.npmmirror.com/@react-navigation/core/download/@react-navigation/core-3.7.9.tgz", "@react-navigation/native@^3.8.4": "https://registry.npmmirror.com/@react-navigation/native/download/@react-navigation/native-3.8.4.tgz", "@sideway/address@^4.1.0": "https://registry.npmmirror.com/@sideway/address/download/@sideway/address-4.1.2.tgz", "@sideway/formula@^3.0.0": "https://registry.npmmirror.com/@sideway/formula/download/@sideway/formula-3.0.0.tgz", "@sideway/pinpoint@^2.0.0": "https://registry.npmmirror.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz", "@sinonjs/commons@^1.7.0": "https://registry.npmmirror.com/@sinonjs/commons/download/@sinonjs/commons-1.8.3.tgz", "@sinonjs/fake-timers@^8.0.1": "https://registry.npmmirror.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-8.1.0.tgz?cache=0&sync_timestamp=1635949916472&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40sinonjs%2Ffake-timers%2Fdownload%2F%40sinonjs%2Ffake-timers-8.1.0.tgz", "@tootallnate/once@1": "https://registry.npmmirror.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz", "@types/babel__core@^7.0.0": "https://registry.npmmirror.com/@types/babel__core/download/@types/babel__core-7.1.16.tgz", "@types/babel__core@^7.1.14": "https://registry.npmmirror.com/@types/babel__core/download/@types/babel__core-7.1.16.tgz", "@types/babel__generator@*": "https://registry.npmmirror.com/@types/babel__generator/download/@types/babel__generator-7.6.3.tgz", "@types/babel__template@*": "https://registry.npmmirror.com/@types/babel__template/download/@types/babel__template-7.4.1.tgz", "@types/babel__traverse@*": "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.14.2.tgz", "@types/babel__traverse@^7.0.4": "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.14.2.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmmirror.com/@types/babel__traverse/download/@types/babel__traverse-7.14.2.tgz", "@types/graceful-fs@^4.1.2": "https://registry.npmmirror.com/@types/graceful-fs/download/@types/graceful-fs-4.1.5.tgz", "@types/hammerjs@^2.0.36": "https://registry.npmmirror.com/@types/hammerjs/download/@types/hammerjs-2.0.40.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmmirror.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.3.tgz", "@types/istanbul-lib-report@*": "https://registry.npmmirror.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmmirror.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz", "@types/json-schema@^7.0.7": "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz?cache=0&sync_timestamp=1632763827525&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fjson-schema%2Fdownload%2F%40types%2Fjson-schema-7.0.9.tgz", "@types/node@*": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "@types/prettier@^2.1.5": "https://registry.npmmirror.com/@types/prettier/download/@types/prettier-2.4.1.tgz", "@types/shallowequal@^1.1.1": "https://registry.npmmirror.com/@types/shallowequal/download/@types/shallowequal-1.1.1.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmmirror.com/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz", "@types/yargs-parser@*": "https://registry.npmmirror.com/@types/yargs-parser/download/@types/yargs-parser-20.2.1.tgz", "@types/yargs@^15.0.0": "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-15.0.14.tgz", "@types/yargs@^16.0.0": "https://registry.npmmirror.com/@types/yargs/download/@types/yargs-16.0.4.tgz", "@typescript-eslint/eslint-plugin@^4.22.1": "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-4.33.0.tgz", "@typescript-eslint/experimental-utils@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-4.33.0.tgz", "@typescript-eslint/parser@^4.22.1": "https://registry.npmmirror.com/@typescript-eslint/parser/download/@typescript-eslint/parser-4.33.0.tgz", "@typescript-eslint/scope-manager@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-4.33.0.tgz", "@typescript-eslint/types@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/types/download/@typescript-eslint/types-4.33.0.tgz", "@typescript-eslint/typescript-estree@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-4.33.0.tgz", "@typescript-eslint/visitor-keys@4.33.0": "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-4.33.0.tgz", "@xmldom/xmldom@~0.7.0": "https://registry.npmmirror.com/@xmldom/xmldom/download/@xmldom/xmldom-0.7.5.tgz", "abab@^2.0.3": "https://registry.npmmirror.com/abab/download/abab-2.0.5.tgz", "abab@^2.0.5": "https://registry.npmmirror.com/abab/download/abab-2.0.5.tgz", "abort-controller@^3.0.0": "https://registry.npmmirror.com/abort-controller/download/abort-controller-3.0.0.tgz", "absolute-path@^0.0.0": "https://registry.npmmirror.com/absolute-path/download/absolute-path-0.0.0.tgz", "accepts@^1.3.7": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "accepts@~1.3.5": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "accepts@~1.3.7": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "acorn-globals@^6.0.0": "https://registry.npmmirror.com/acorn-globals/download/acorn-globals-6.0.0.tgz", "acorn-jsx@^5.3.1": "https://registry.npmmirror.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "acorn-walk@^7.1.1": "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-7.2.0.tgz", "acorn@^7.1.1": "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz", "acorn@^8.2.4": "https://registry.npmmirror.com/acorn/download/acorn-8.5.0.tgz", "acorn@^8.5.0": "https://registry.npmmirror.com/acorn/download/acorn-8.5.0.tgz", "agent-base@6": "https://registry.npmmirror.com/agent-base/download/agent-base-6.0.2.tgz", "ajv@^6.10.0": "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1632363896657&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1632363896657&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz", "anser@^1.4.9": "https://registry.npmmirror.com/anser/download/anser-1.4.10.tgz?cache=0&sync_timestamp=1634204728307&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fanser%2Fdownload%2Fanser-1.4.10.tgz", "ansi-colors@^4.1.1": "https://registry.npmmirror.com/ansi-colors/download/ansi-colors-4.1.1.tgz", "ansi-escapes@^4.2.1": "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz", "ansi-fragments@^0.2.1": "https://registry.npmmirror.com/ansi-fragments/download/ansi-fragments-0.2.1.tgz", "ansi-regex@^4.1.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-4.1.0.tgz", "ansi-regex@^5.0.0": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "ansi-styles@^3.2.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "ansi-styles@^3.2.1": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-5.2.0.tgz", "anymatch@^2.0.0": "https://registry.npmmirror.com/anymatch/download/anymatch-2.0.0.tgz", "anymatch@^3.0.3": "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.2.tgz", "appdirsjs@^1.2.4": "https://registry.npmmirror.com/appdirsjs/download/appdirsjs-1.2.6.tgz", "argparse@^1.0.7": "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmmirror.com/argparse/download/argparse-2.0.1.tgz", "arr-diff@^4.0.0": "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz", "arr-flatten@^1.1.0": "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz", "arr-union@^3.1.0": "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz", "array-filter@~0.0.0": "https://registry.npmmirror.com/array-filter/download/array-filter-0.0.1.tgz", "array-includes@^3.1.3": "https://registry.npmmirror.com/array-includes/download/array-includes-3.1.4.tgz?cache=0&sync_timestamp=1633411730863&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Farray-includes%2Fdownload%2Farray-includes-3.1.4.tgz", "array-map@~0.0.0": "https://registry.npmmirror.com/array-map/download/array-map-0.0.0.tgz", "array-reduce@~0.0.0": "https://registry.npmmirror.com/array-reduce/download/array-reduce-0.0.0.tgz", "array-tree-filter@~2.1.0": "https://registry.npmmirror.com/array-tree-filter/download/array-tree-filter-2.1.0.tgz", "array-union@^2.1.0": "https://registry.npmmirror.com/array-union/download/array-union-2.1.0.tgz", "array-unique@^0.3.2": "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz", "array.prototype.flatmap@^1.2.4": "https://registry.npmmirror.com/array.prototype.flatmap/download/array.prototype.flatmap-1.2.5.tgz", "asap@~2.0.3": "https://registry.npmmirror.com/asap/download/asap-2.0.6.tgz", "asap@~2.0.6": "https://registry.npmmirror.com/asap/download/asap-2.0.6.tgz", "assign-symbols@^1.0.0": "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz", "ast-types@0.14.2": "https://registry.npmmirror.com/ast-types/download/ast-types-0.14.2.tgz", "astral-regex@^1.0.0": "https://registry.npmmirror.com/astral-regex/download/astral-regex-1.0.0.tgz", "async-limiter@~1.0.0": "https://registry.npmmirror.com/async-limiter/download/async-limiter-1.0.1.tgz", "async@^2.4.0": "https://registry.npmmirror.com/async/download/async-2.6.3.tgz", "asynckit@^0.4.0": "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmmirror.com/at-least-node/download/at-least-node-1.0.0.tgz", "atob@^2.1.2": "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz", "babel-core@^7.0.0-bridge.0": "https://registry.npmmirror.com/babel-core/download/babel-core-7.0.0-bridge.0.tgz", "babel-eslint@^10.1.0": "https://registry.npmmirror.com/babel-eslint/download/babel-eslint-10.1.0.tgz", "babel-jest@^27.3.1": "https://registry.npmmirror.com/babel-jest/download/babel-jest-27.3.1.tgz?cache=0&sync_timestamp=1634626745680&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-jest%2Fdownload%2Fbabel-jest-27.3.1.tgz", "babel-plugin-import@^1.13.3": "https://registry.npmmirror.com/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz", "babel-plugin-istanbul@^6.0.0": "https://registry.npmmirror.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^27.2.0": "https://registry.npmmirror.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-27.2.0.tgz", "babel-plugin-module-resolver@^4.1.0": "https://registry.npmmirror.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-4.1.0.tgz", "babel-plugin-polyfill-corejs2@^0.2.3": "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.2.3.tgz", "babel-plugin-polyfill-corejs3@^0.3.0": "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.3.0.tgz?cache=0&sync_timestamp=1635567635564&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.3.0.tgz", "babel-plugin-polyfill-regenerator@^0.2.3": "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.2.3.tgz?cache=0&sync_timestamp=1635567640308&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.2.3.tgz", "babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0": "https://registry.npmmirror.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmmirror.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz", "babel-preset-fbjs@^3.4.0": "https://registry.npmmirror.com/babel-preset-fbjs/download/babel-preset-fbjs-3.4.0.tgz", "babel-preset-jest@^27.2.0": "https://registry.npmmirror.com/babel-preset-jest/download/babel-preset-jest-27.2.0.tgz", "babel-runtime@^6.x": "https://registry.npmmirror.com/babel-runtime/download/babel-runtime-6.26.0.tgz", "balanced-match@^1.0.0": "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz", "base64-js@^1.1.2": "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz", "base64-js@^1.2.3": "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz", "base64-js@^1.5.1": "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz", "base@^0.11.1": "https://registry.npmmirror.com/base/download/base-0.11.2.tgz", "big-integer@1.6.x": "https://registry.npmmirror.com/big-integer/download/big-integer-1.6.50.tgz?cache=0&sync_timestamp=1634050154371&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbig-integer%2Fdownload%2Fbig-integer-1.6.50.tgz", "bplist-creator@0.1.0": "https://registry.npmmirror.com/bplist-creator/download/bplist-creator-0.1.0.tgz", "bplist-parser@0.3.0": "https://registry.npmmirror.com/bplist-parser/download/bplist-parser-0.3.0.tgz", "brace-expansion@^1.1.7": "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "braces@^2.3.1": "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz", "braces@^3.0.1": "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz", "browser-process-hrtime@^1.0.0": "https://registry.npmmirror.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz", "browserslist@^4.21.3": "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.4.tgz", "browserslist@^4.21.4": "https://registry.npmmirror.com/browserslist/-/browserslist-4.21.4.tgz", "bser@2.1.1": "https://registry.npmmirror.com/bser/download/bser-2.1.1.tgz", "buffer-from@^1.0.0": "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.2.tgz", "builtin-modules@^1.1.1": "https://registry.npmmirror.com/builtin-modules/download/builtin-modules-1.1.1.tgz", "bytes@3.0.0": "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz", "cache-base@^1.0.1": "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz", "call-bind@^1.0.0": "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz", "call-bind@^1.0.2": "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz", "caller-callsite@^2.0.0": "https://registry.npmmirror.com/caller-callsite/download/caller-callsite-2.0.0.tgz?cache=0&sync_timestamp=1633616961620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-callsite%2Fdownload%2Fcaller-callsite-2.0.0.tgz", "caller-path@^2.0.0": "https://registry.npmmirror.com/caller-path/download/caller-path-2.0.0.tgz?cache=0&sync_timestamp=1633674209796&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaller-path%2Fdownload%2Fcaller-path-2.0.0.tgz", "callsites@^2.0.0": "https://registry.npmmirror.com/callsites/download/callsites-2.0.0.tgz", "callsites@^3.0.0": "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz", "camelcase@^5.0.0": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "camelcase@^5.3.1": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "camelcase@^6.0.0": "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.0.tgz", "camelcase@^6.2.0": "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.0.tgz", "caniuse-lite@^1.0.30001400": "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001434.tgz", "capture-exit@^2.0.0": "https://registry.npmmirror.com/capture-exit/download/capture-exit-2.0.0.tgz", "chalk@^2.0.0": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.0.1": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.3.0": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz", "chalk@^4.0.0": "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz", "char-regex@^1.0.2": "https://registry.npmmirror.com/char-regex/download/char-regex-1.0.2.tgz", "ci-info@^2.0.0": "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz", "ci-info@^3.2.0": "https://registry.npmmirror.com/ci-info/download/ci-info-3.2.0.tgz", "cjs-module-lexer@^1.0.0": "https://registry.npmmirror.com/cjs-module-lexer/download/cjs-module-lexer-1.2.2.tgz", "class-utils@^0.3.5": "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz", "cli-cursor@^2.1.0": "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz", "cli-spinners@^2.0.0": "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.6.1.tgz?cache=0&sync_timestamp=1633109592807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcli-spinners%2Fdownload%2Fcli-spinners-2.6.1.tgz", "cliui@^6.0.0": "https://registry.npmmirror.com/cliui/download/cliui-6.0.0.tgz", "cliui@^7.0.2": "https://registry.npmmirror.com/cliui/download/cliui-7.0.4.tgz", "clone-deep@^4.0.1": "https://registry.npmmirror.com/clone-deep/download/clone-deep-4.0.1.tgz", "clone@^1.0.2": "https://registry.npmmirror.com/clone/download/clone-1.0.4.tgz", "co@^4.6.0": "https://registry.npmmirror.com/co/download/co-4.6.0.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmmirror.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz", "collection-visit@^1.0.0": "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz", "color-convert@^1.9.0": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "color-convert@^1.9.3": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "color-name@^1.0.0": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz", "color-string@^1.6.0": "https://registry.npmmirror.com/color-string/download/color-string-1.6.0.tgz", "color@^3.1.3": "https://registry.npmmirror.com/color/download/color-3.2.1.tgz", "colorette@^1.0.7": "https://registry.npmmirror.com/colorette/download/colorette-1.4.0.tgz?cache=0&sync_timestamp=1633673099786&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcolorette%2Fdownload%2Fcolorette-1.4.0.tgz", "colors@^1.1.2": "https://registry.npmmirror.com/colors/download/colors-1.4.0.tgz", "combined-stream@^1.0.8": "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz", "command-exists@^1.2.8": "https://registry.npmmirror.com/command-exists/download/command-exists-1.2.9.tgz", "commander@^2.12.1": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "commander@^2.19.0": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "commander@~2.13.0": "https://registry.npmmirror.com/commander/download/commander-2.13.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.13.0.tgz", "commondir@^1.0.1": "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz", "component-emitter@^1.2.1": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz", "compressible@~2.0.16": "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz", "compression@^1.7.1": "https://registry.npmmirror.com/compression/download/compression-1.7.4.tgz", "concat-map@0.0.1": "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz", "connect@^3.6.5": "https://registry.npmmirror.com/connect/download/connect-3.7.0.tgz", "convert-source-map@^1.4.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1632741882507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz", "convert-source-map@^1.6.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1632741882507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1632741882507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz", "copy-descriptor@^0.1.0": "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "core-js-compat@^3.18.0": "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.26.1.tgz", "core-js@^2.4.0": "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz", "core-util-is@~1.0.0": "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.3.tgz", "cosmiconfig@^5.0.5": "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz", "cosmiconfig@^5.1.0": "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz", "cross-fetch@^3.0.4": "https://registry.npmmirror.com/cross-fetch/download/cross-fetch-3.1.4.tgz", "cross-spawn@^6.0.0": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "cross-spawn@^7.0.2": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.3.tgz", "cross-spawn@^7.0.3": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.3.tgz", "cssom@^0.4.4": "https://registry.npmmirror.com/cssom/download/cssom-0.4.4.tgz", "cssom@~0.3.6": "https://registry.npmmirror.com/cssom/download/cssom-0.3.8.tgz", "cssstyle@^2.3.0": "https://registry.npmmirror.com/cssstyle/download/cssstyle-2.3.0.tgz", "data-urls@^2.0.0": "https://registry.npmmirror.com/data-urls/download/data-urls-2.0.0.tgz", "dayjs@^1.8.15": "https://registry.npmmirror.com/dayjs/download/dayjs-1.10.7.tgz", "debug@2.6.9": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@4": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz", "debug@^2.2.0": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@^2.3.3": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz", "debug@^4.1.0": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz", "debug@^4.1.1": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz", "debug@^4.3.1": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz", "debug@^4.3.2": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz", "decamelize@^1.2.0": "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055713394&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz", "decimal.js@^10.2.1": "https://registry.npmmirror.com/decimal.js/download/decimal.js-10.3.1.tgz", "decode-uri-component@^0.2.0": "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "dedent@^0.7.0": "https://registry.npmmirror.com/dedent/download/dedent-0.7.0.tgz", "deep-is@^0.1.3": "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.4.tgz", "deep-is@~0.1.3": "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.4.tgz", "deepmerge@^3.2.0": "https://registry.npmmirror.com/deepmerge/download/deepmerge-3.3.0.tgz", "deepmerge@^4.2.2": "https://registry.npmmirror.com/deepmerge/download/deepmerge-4.2.2.tgz", "defaults@^1.0.3": "https://registry.npmmirror.com/defaults/download/defaults-1.0.3.tgz", "define-properties@^1.1.3": "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz", "define-property@^0.2.5": "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz", "define-property@^1.0.0": "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz", "define-property@^2.0.2": "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz", "delayed-stream@~1.0.0": "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "denodeify@^1.2.1": "https://registry.npmmirror.com/denodeify/download/denodeify-1.2.1.tgz", "depd@~1.1.2": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz?cache=0&sync_timestamp=1632469617288&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdepd%2Fdownload%2Fdepd-1.1.2.tgz", "destroy@~1.0.4": "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz", "detect-newline@^3.0.0": "https://registry.npmmirror.com/detect-newline/download/detect-newline-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdetect-newline%2Fdownload%2Fdetect-newline-3.1.0.tgz", "diff-sequences@^27.0.6": "https://registry.npmmirror.com/diff-sequences/download/diff-sequences-27.0.6.tgz", "diff@^4.0.1": "https://registry.npmmirror.com/diff/download/diff-4.0.2.tgz", "dir-glob@^3.0.1": "https://registry.npmmirror.com/dir-glob/download/dir-glob-3.0.1.tgz", "doctrine@^2.1.0": "https://registry.npmmirror.com/doctrine/download/doctrine-2.1.0.tgz", "doctrine@^3.0.0": "https://registry.npmmirror.com/doctrine/download/doctrine-3.0.0.tgz", "domexception@^2.0.1": "https://registry.npmmirror.com/domexception/download/domexception-2.0.1.tgz?cache=0&sync_timestamp=1633538712137&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdomexception%2Fdownload%2Fdomexception-2.0.1.tgz", "ee-first@1.1.1": "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz", "electron-to-chromium@^1.4.251": "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz", "emittery@^0.8.1": "https://registry.npmmirror.com/emittery/download/emittery-0.8.1.tgz", "emoji-regex@^8.0.0": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632752198735&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz", "encodeurl@~1.0.2": "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz", "end-of-stream@^1.1.0": "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz?cache=0&sync_timestamp=1632469585035&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fend-of-stream%2Fdownload%2Fend-of-stream-1.4.4.tgz", "enquirer@^2.3.5": "https://registry.npmmirror.com/enquirer/download/enquirer-2.3.6.tgz", "envinfo@^7.7.2": "https://registry.npmmirror.com/envinfo/download/envinfo-7.8.1.tgz", "error-ex@^1.3.1": "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmmirror.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz", "errorhandler@^1.5.0": "https://registry.npmmirror.com/errorhandler/download/errorhandler-1.5.1.tgz", "es-abstract@^1.19.0": "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.19.1.tgz?cache=0&sync_timestamp=1633234258828&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.19.1.tgz", "es-abstract@^1.19.1": "https://registry.npmmirror.com/es-abstract/download/es-abstract-1.19.1.tgz?cache=0&sync_timestamp=1633234258828&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-abstract%2Fdownload%2Fes-abstract-1.19.1.tgz", "es-to-primitive@^1.2.1": "https://registry.npmmirror.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz", "escalade@^3.1.1": "https://registry.npmmirror.com/escalade/download/escalade-3.1.1.tgz", "escape-html@~1.0.3": "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz", "escape-string-regexp@2.0.0": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "escodegen@^2.0.0": "https://registry.npmmirror.com/escodegen/download/escodegen-2.0.0.tgz", "eslint-config-prettier@^6.10.1": "https://registry.npmmirror.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz", "eslint-plugin-eslint-comments@^3.1.2": "https://registry.npmmirror.com/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz", "eslint-plugin-flowtype@2.50.3": "https://registry.npmmirror.com/eslint-plugin-flowtype/download/eslint-plugin-flowtype-2.50.3.tgz?cache=0&sync_timestamp=1635741709822&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-flowtype%2Fdownload%2Feslint-plugin-flowtype-2.50.3.tgz", "eslint-plugin-jest@22.4.1": "https://registry.npmmirror.com/eslint-plugin-jest/download/eslint-plugin-jest-22.4.1.tgz?cache=0&sync_timestamp=1636051559816&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-plugin-jest%2Fdownload%2Feslint-plugin-jest-22.4.1.tgz", "eslint-plugin-prettier@3.1.2": "https://registry.npmmirror.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.1.2.tgz", "eslint-plugin-react-hooks@^4.0.7": "https://registry.npmmirror.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.2.0.tgz", "eslint-plugin-react-native-globals@^0.1.1": "https://registry.npmmirror.com/eslint-plugin-react-native-globals/download/eslint-plugin-react-native-globals-0.1.2.tgz", "eslint-plugin-react-native@^3.10.0": "https://registry.npmmirror.com/eslint-plugin-react-native/download/eslint-plugin-react-native-3.11.0.tgz", "eslint-plugin-react@^7.20.0": "https://registry.npmmirror.com/eslint-plugin-react/download/eslint-plugin-react-7.26.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz", "eslint-scope@^6.0.0": "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-6.0.0.tgz", "eslint-utils@^3.0.0": "https://registry.npmmirror.com/eslint-utils/download/eslint-utils-3.0.0.tgz?cache=0&sync_timestamp=1632470817621&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-utils%2Fdownload%2Feslint-utils-3.0.0.tgz", "eslint-visitor-keys@^1.0.0": "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz", "eslint-visitor-keys@^2.0.0": "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^3.0.0": "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-3.0.0.tgz", "eslint@^8.2.0": "https://registry.npmmirror.com/eslint/download/eslint-8.2.0.tgz?cache=0&sync_timestamp=1636158100294&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint%2Fdownload%2Feslint-8.2.0.tgz", "espree@^9.0.0": "https://registry.npmmirror.com/espree/download/espree-9.0.0.tgz", "esprima@^4.0.0": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz", "esprima@~4.0.0": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz", "esquery@^1.4.0": "https://registry.npmmirror.com/esquery/download/esquery-1.4.0.tgz", "esrecurse@^4.3.0": "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237716974&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz", "esutils@^2.0.2": "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz", "event-target-shim@^5.0.0": "https://registry.npmmirror.com/event-target-shim/download/event-target-shim-5.0.1.tgz", "event-target-shim@^5.0.1": "https://registry.npmmirror.com/event-target-shim/download/event-target-shim-5.0.1.tgz", "exec-sh@^0.3.2": "https://registry.npmmirror.com/exec-sh/download/exec-sh-0.3.6.tgz", "execa@^1.0.0": "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz", "execa@^5.0.0": "https://registry.npmmirror.com/execa/download/execa-5.1.1.tgz", "exit@^0.1.2": "https://registry.npmmirror.com/exit/download/exit-0.1.2.tgz", "expand-brackets@^2.1.4": "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz", "expect@^27.3.1": "https://registry.npmmirror.com/expect/download/expect-27.3.1.tgz?cache=0&sync_timestamp=1634626746529&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexpect%2Fdownload%2Fexpect-27.3.1.tgz", "extend-shallow@^2.0.1": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz", "extend-shallow@^3.0.0": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz", "extend-shallow@^3.0.2": "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz", "extglob@^2.0.4": "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "fast-diff@^1.1.2": "https://registry.npmmirror.com/fast-diff/download/fast-diff-1.2.0.tgz", "fast-glob@^3.1.1": "https://registry.npmmirror.com/fast-glob/download/fast-glob-3.2.7.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "fast-levenshtein@~2.0.6": "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "fastq@^1.6.0": "https://registry.npmmirror.com/fastq/download/fastq-1.13.0.tgz", "fb-watchman@^2.0.0": "https://registry.npmmirror.com/fb-watchman/download/fb-watchman-2.0.1.tgz", "fbjs-css-vars@^1.0.0": "https://registry.npmmirror.com/fbjs-css-vars/download/fbjs-css-vars-1.0.2.tgz", "fbjs@^3.0.0": "https://registry.npmmirror.com/fbjs/download/fbjs-3.0.1.tgz?cache=0&sync_timestamp=1635212744753&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffbjs%2Fdownload%2Ffbjs-3.0.1.tgz", "file-entry-cache@^6.0.1": "https://registry.npmmirror.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz", "fill-range@^4.0.0": "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz", "fill-range@^7.0.1": "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz", "filter-obj@^1.1.0": "https://registry.npmmirror.com/filter-obj/download/filter-obj-1.1.0.tgz", "finalhandler@1.1.2": "https://registry.npmmirror.com/finalhandler/download/finalhandler-1.1.2.tgz", "find-babel-config@^1.2.0": "https://registry.npmmirror.com/find-babel-config/download/find-babel-config-1.2.0.tgz", "find-cache-dir@^2.0.0": "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz", "find-up@^3.0.0": "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz", "find-up@^4.0.0": "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-4.1.0.tgz", "find-up@~5.0.0": "https://registry.npmmirror.com/find-up/download/find-up-5.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmmirror.com/flat-cache/download/flat-cache-3.0.4.tgz", "flatted@^3.1.0": "https://registry.npmmirror.com/flatted/download/flatted-3.2.2.tgz", "flow-bin@0.113.0": "https://registry.npmmirror.com/flow-bin/download/flow-bin-0.113.0.tgz", "flow-parser@0.*": "https://registry.npmmirror.com/flow-parser/download/flow-parser-0.121.0.tgz?cache=0&sync_timestamp=1635505769718&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflow-parser%2Fdownload%2Fflow-parser-0.121.0.tgz", "flow-parser@^0.121.0": "https://registry.npmmirror.com/flow-parser/download/flow-parser-0.121.0.tgz?cache=0&sync_timestamp=1635505769718&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflow-parser%2Fdownload%2Fflow-parser-0.121.0.tgz", "for-in@^1.0.2": "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz", "form-data@^3.0.0": "https://registry.npmmirror.com/form-data/download/form-data-3.0.1.tgz", "fragment-cache@^0.2.1": "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz", "fresh@0.5.2": "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz", "fs-extra@9.0.0": "https://registry.npmmirror.com/fs-extra/download/fs-extra-9.0.0.tgz", "fs-extra@^1.0.0": "https://registry.npmmirror.com/fs-extra/download/fs-extra-1.0.0.tgz", "fs-extra@^8.1.0": "https://registry.npmmirror.com/fs-extra/download/fs-extra-8.1.0.tgz", "fs.realpath@^1.0.0": "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "fsevents@^2.1.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "fsevents@^2.3.2": "https://registry.npmmirror.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.1": "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz", "functional-red-black-tree@^1.0.1": "https://registry.npmmirror.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmmirror.com/gensync/download/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.1": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "get-caller-file@^2.0.5": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.0.2": "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz", "get-intrinsic@^1.1.0": "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz", "get-intrinsic@^1.1.1": "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz", "get-package-type@^0.1.0": "https://registry.npmmirror.com/get-package-type/download/get-package-type-0.1.0.tgz", "get-stdin@^6.0.0": "https://registry.npmmirror.com/get-stdin/download/get-stdin-6.0.0.tgz", "get-stream@^4.0.0": "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz", "get-stream@^6.0.0": "https://registry.npmmirror.com/get-stream/download/get-stream-6.0.1.tgz", "get-symbol-description@^1.0.0": "https://registry.npmmirror.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz", "get-value@^2.0.3": "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz", "get-value@^2.0.6": "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz", "getenv@^1.0.0": "https://registry.npmmirror.com/getenv/download/getenv-1.0.0.tgz", "glob-parent@^5.1.2": "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953971963&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz", "glob-parent@^6.0.1": "https://registry.npmmirror.com/glob-parent/download/glob-parent-6.0.2.tgz?cache=0&sync_timestamp=1632953971963&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-6.0.2.tgz", "glob@7.1.6": "https://registry.npmmirror.com/glob/download/glob-7.1.6.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.1.6.tgz", "glob@^7.1.1": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "glob@^7.1.2": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "glob@^7.1.3": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "glob@^7.1.4": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "glob@^7.1.6": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&sync_timestamp=1632406336913&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "globals@^11.1.0": "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz", "globals@^13.6.0": "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz", "globals@^13.9.0": "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz", "globby@^11.0.3": "https://registry.npmmirror.com/globby/download/globby-11.0.4.tgz", "graceful-fs@^4.1.11": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.1.2": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.1.3": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.1.6": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.1.9": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.2.0": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "graceful-fs@^4.2.4": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz", "has-bigints@^1.0.1": "https://registry.npmmirror.com/has-bigints/download/has-bigints-1.0.1.tgz", "has-flag@^3.0.0": "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz", "has-symbols@^1.0.1": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz", "has-symbols@^1.0.2": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz", "has-tostringtag@^1.0.0": "https://registry.npmmirror.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz", "has-value@^0.3.1": "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz", "has-value@^1.0.0": "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz", "has-values@^0.1.4": "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz", "has-values@^1.0.0": "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz", "has@^1.0.3": "https://registry.npmmirror.com/has/download/has-1.0.3.tgz", "hermes-engine@~0.9.0": "https://registry.npmmirror.com/hermes-engine/download/hermes-engine-0.9.0.tgz", "hermes-parser@0.4.7": "https://registry.npmmirror.com/hermes-parser/download/hermes-parser-0.4.7.tgz", "hermes-profile-transformer@^0.0.6": "https://registry.npmmirror.com/hermes-profile-transformer/download/hermes-profile-transformer-0.0.6.tgz", "hoist-non-react-statics@^2.3.1": "https://registry.npmmirror.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz", "hoist-non-react-statics@^3.3.0": "https://registry.npmmirror.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz", "hoist-non-react-statics@^3.3.2": "https://registry.npmmirror.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz", "html-encoding-sniffer@^2.0.1": "https://registry.npmmirror.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz", "html-escaper@^2.0.0": "https://registry.npmmirror.com/html-escaper/download/html-escaper-2.0.2.tgz", "http-errors@~1.7.2": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz", "http-proxy-agent@^4.0.1": "https://registry.npmmirror.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz", "https-proxy-agent@^5.0.0": "https://registry.npmmirror.com/https-proxy-agent/download/https-proxy-agent-5.0.0.tgz", "human-signals@^2.1.0": "https://registry.npmmirror.com/human-signals/download/human-signals-2.1.0.tgz", "iconv-lite@0.4.24": "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "ignore@^4.0.6": "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz", "ignore@^5.0.5": "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz", "ignore@^5.1.4": "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz", "ignore@^5.1.8": "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz", "image-size@^0.6.0": "https://registry.npmmirror.com/image-size/download/image-size-0.6.3.tgz", "import-fresh@^2.0.0": "https://registry.npmmirror.com/import-fresh/download/import-fresh-2.0.0.tgz", "import-fresh@^3.0.0": "https://registry.npmmirror.com/import-fresh/download/import-fresh-3.3.0.tgz", "import-fresh@^3.2.1": "https://registry.npmmirror.com/import-fresh/download/import-fresh-3.3.0.tgz", "import-local@^3.0.2": "https://registry.npmmirror.com/import-local/download/import-local-3.0.3.tgz", "imurmurhash@^0.1.4": "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@2.0.4": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "internal-slot@^1.0.3": "https://registry.npmmirror.com/internal-slot/download/internal-slot-1.0.3.tgz", "invariant@2.2.4": "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz", "invariant@^2.2.4": "https://registry.npmmirror.com/invariant/download/invariant-2.2.4.tgz", "ip@^1.1.5": "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz", "is-accessor-descriptor@^0.1.6": "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "is-accessor-descriptor@^1.0.0": "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "is-arrayish@^0.2.1": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "is-arrayish@^0.3.1": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "is-bigint@^1.0.1": "https://registry.npmmirror.com/is-bigint/download/is-bigint-1.0.4.tgz", "is-boolean-object@^1.1.0": "https://registry.npmmirror.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz", "is-buffer@^1.1.5": "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz", "is-callable@^1.1.4": "https://registry.npmmirror.com/is-callable/download/is-callable-1.2.4.tgz", "is-callable@^1.2.4": "https://registry.npmmirror.com/is-callable/download/is-callable-1.2.4.tgz", "is-ci@^2.0.0": "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1635261090481&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz", "is-core-module@^2.2.0": "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz?cache=0&sync_timestamp=1634236434261&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.8.0.tgz", "is-data-descriptor@^0.1.4": "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "is-data-descriptor@^1.0.0": "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "is-date-object@^1.0.1": "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.5.tgz", "is-descriptor@^0.1.0": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz", "is-descriptor@^1.0.0": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz", "is-descriptor@^1.0.2": "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz", "is-directory@^0.3.1": "https://registry.npmmirror.com/is-directory/download/is-directory-0.3.1.tgz", "is-extendable@^0.1.0": "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz", "is-extendable@^0.1.1": "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz", "is-extendable@^1.0.1": "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz", "is-extglob@^2.1.1": "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz", "is-fullwidth-code-point@^2.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmmirror.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934573225&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934573225&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934573225&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz", "is-negative-zero@^2.0.1": "https://registry.npmmirror.com/is-negative-zero/download/is-negative-zero-2.0.1.tgz", "is-number-object@^1.0.4": "https://registry.npmmirror.com/is-number-object/download/is-number-object-1.0.6.tgz", "is-number@^3.0.0": "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz", "is-plain-obj@^2.1.0": "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz", "is-plain-object@^2.0.3": "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "is-plain-object@^2.0.4": "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz", "is-potential-custom-element-name@^1.0.1": "https://registry.npmmirror.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz", "is-regex@^1.1.4": "https://registry.npmmirror.com/is-regex/download/is-regex-1.1.4.tgz", "is-shared-array-buffer@^1.0.1": "https://registry.npmmirror.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.1.tgz?cache=0&sync_timestamp=1633062271086&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-shared-array-buffer%2Fdownload%2Fis-shared-array-buffer-1.0.1.tgz", "is-stream@^1.1.0": "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz", "is-stream@^2.0.0": "https://registry.npmmirror.com/is-stream/download/is-stream-2.0.1.tgz", "is-string@^1.0.5": "https://registry.npmmirror.com/is-string/download/is-string-1.0.7.tgz", "is-string@^1.0.7": "https://registry.npmmirror.com/is-string/download/is-string-1.0.7.tgz", "is-symbol@^1.0.2": "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.4.tgz", "is-symbol@^1.0.3": "https://registry.npmmirror.com/is-symbol/download/is-symbol-1.0.4.tgz", "is-typedarray@^1.0.0": "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "is-weakref@^1.0.1": "https://registry.npmmirror.com/is-weakref/download/is-weakref-1.0.1.tgz", "is-windows@^1.0.2": "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz", "is-wsl@^1.1.0": "https://registry.npmmirror.com/is-wsl/download/is-wsl-1.1.0.tgz", "isarray@0.0.1": "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz", "isarray@1.0.0": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "isarray@~1.0.0": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz", "isobject@^2.0.0": "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz", "isobject@^3.0.0": "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz", "isobject@^3.0.1": "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz?cache=0&sync_timestamp=1634527381492&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fistanbul-lib-coverage%2Fdownload%2Fistanbul-lib-coverage-3.2.0.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmmirror.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz?cache=0&sync_timestamp=1634527381492&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fistanbul-lib-coverage%2Fdownload%2Fistanbul-lib-coverage-3.2.0.tgz", "istanbul-lib-instrument@^4.0.3": "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmmirror.com/istanbul-lib-instrument/download/istanbul-lib-instrument-5.1.0.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmmirror.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmmirror.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz", "istanbul-reports@^3.0.2": "https://registry.npmmirror.com/istanbul-reports/download/istanbul-reports-3.0.5.tgz", "jest-changed-files@^27.3.0": "https://registry.npmmirror.com/jest-changed-files/download/jest-changed-files-27.3.0.tgz", "jest-circus@^27.3.1": "https://registry.npmmirror.com/jest-circus/download/jest-circus-27.3.1.tgz?cache=0&sync_timestamp=1634627345495&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-circus%2Fdownload%2Fjest-circus-27.3.1.tgz", "jest-cli@^27.3.1": "https://registry.npmmirror.com/jest-cli/download/jest-cli-27.3.1.tgz?cache=0&sync_timestamp=1634626754387&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-cli%2Fdownload%2Fjest-cli-27.3.1.tgz", "jest-config@^27.3.1": "https://registry.npmmirror.com/jest-config/download/jest-config-27.3.1.tgz?cache=0&sync_timestamp=1634626716976&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-config%2Fdownload%2Fjest-config-27.3.1.tgz", "jest-diff@^27.3.1": "https://registry.npmmirror.com/jest-diff/download/jest-diff-27.3.1.tgz?cache=0&sync_timestamp=1634626740654&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-diff%2Fdownload%2Fjest-diff-27.3.1.tgz", "jest-docblock@^27.0.6": "https://registry.npmmirror.com/jest-docblock/download/jest-docblock-27.0.6.tgz", "jest-each@^27.3.1": "https://registry.npmmirror.com/jest-each/download/jest-each-27.3.1.tgz?cache=0&sync_timestamp=1634626724382&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-each%2Fdownload%2Fjest-each-27.3.1.tgz", "jest-environment-jsdom@^27.3.1": "https://registry.npmmirror.com/jest-environment-jsdom/download/jest-environment-jsdom-27.3.1.tgz?cache=0&sync_timestamp=1634626714740&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-environment-jsdom%2Fdownload%2Fjest-environment-jsdom-27.3.1.tgz", "jest-environment-node@^27.3.1": "https://registry.npmmirror.com/jest-environment-node/download/jest-environment-node-27.3.1.tgz?cache=0&sync_timestamp=1634626741234&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-environment-node%2Fdownload%2Fjest-environment-node-27.3.1.tgz", "jest-get-type@^26.3.0": "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-26.3.0.tgz?cache=0&sync_timestamp=1634626733231&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-get-type%2Fdownload%2Fjest-get-type-26.3.0.tgz", "jest-get-type@^27.3.1": "https://registry.npmmirror.com/jest-get-type/download/jest-get-type-27.3.1.tgz?cache=0&sync_timestamp=1634626733231&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-get-type%2Fdownload%2Fjest-get-type-27.3.1.tgz", "jest-haste-map@^26.5.2": "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz?cache=0&sync_timestamp=1634626734781&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-haste-map%2Fdownload%2Fjest-haste-map-26.6.2.tgz", "jest-haste-map@^27.3.1": "https://registry.npmmirror.com/jest-haste-map/download/jest-haste-map-27.3.1.tgz?cache=0&sync_timestamp=1634626734781&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-haste-map%2Fdownload%2Fjest-haste-map-27.3.1.tgz", "jest-jasmine2@^27.3.1": "https://registry.npmmirror.com/jest-jasmine2/download/jest-jasmine2-27.3.1.tgz?cache=0&sync_timestamp=1634626719825&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-jasmine2%2Fdownload%2Fjest-jasmine2-27.3.1.tgz", "jest-leak-detector@^27.3.1": "https://registry.npmmirror.com/jest-leak-detector/download/jest-leak-detector-27.3.1.tgz?cache=0&sync_timestamp=1634626738473&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-leak-detector%2Fdownload%2Fjest-leak-detector-27.3.1.tgz", "jest-matcher-utils@^27.3.1": "https://registry.npmmirror.com/jest-matcher-utils/download/jest-matcher-utils-27.3.1.tgz?cache=0&sync_timestamp=1634626742177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-matcher-utils%2Fdownload%2Fjest-matcher-utils-27.3.1.tgz", "jest-message-util@^27.3.1": "https://registry.npmmirror.com/jest-message-util/download/jest-message-util-27.3.1.tgz?cache=0&sync_timestamp=1634626735326&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-message-util%2Fdownload%2Fjest-message-util-27.3.1.tgz", "jest-mock@^27.3.0": "https://registry.npmmirror.com/jest-mock/download/jest-mock-27.3.0.tgz", "jest-pnp-resolver@^1.2.2": "https://registry.npmmirror.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.2.tgz", "jest-regex-util@^26.0.0": "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz", "jest-regex-util@^27.0.6": "https://registry.npmmirror.com/jest-regex-util/download/jest-regex-util-27.0.6.tgz", "jest-resolve-dependencies@^27.3.1": "https://registry.npmmirror.com/jest-resolve-dependencies/download/jest-resolve-dependencies-27.3.1.tgz?cache=0&sync_timestamp=1634626724098&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-resolve-dependencies%2Fdownload%2Fjest-resolve-dependencies-27.3.1.tgz", "jest-resolve@^27.3.1": "https://registry.npmmirror.com/jest-resolve/download/jest-resolve-27.3.1.tgz?cache=0&sync_timestamp=1634626716645&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-resolve%2Fdownload%2Fjest-resolve-27.3.1.tgz", "jest-runner@^27.3.1": "https://registry.npmmirror.com/jest-runner/download/jest-runner-27.3.1.tgz?cache=0&sync_timestamp=1634626715302&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-runner%2Fdownload%2Fjest-runner-27.3.1.tgz", "jest-runtime@^27.3.1": "https://registry.npmmirror.com/jest-runtime/download/jest-runtime-27.3.1.tgz?cache=0&sync_timestamp=1634626721625&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-runtime%2Fdownload%2Fjest-runtime-27.3.1.tgz", "jest-serializer@^26.6.2": "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-26.6.2.tgz", "jest-serializer@^27.0.6": "https://registry.npmmirror.com/jest-serializer/download/jest-serializer-27.0.6.tgz", "jest-snapshot@^27.3.1": "https://registry.npmmirror.com/jest-snapshot/download/jest-snapshot-27.3.1.tgz?cache=0&sync_timestamp=1634626720436&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-snapshot%2Fdownload%2Fjest-snapshot-27.3.1.tgz", "jest-util@^26.6.2": "https://registry.npmmirror.com/jest-util/download/jest-util-26.6.2.tgz?cache=0&sync_timestamp=1634626734205&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-util%2Fdownload%2Fjest-util-26.6.2.tgz", "jest-util@^27.3.1": "https://registry.npmmirror.com/jest-util/download/jest-util-27.3.1.tgz?cache=0&sync_timestamp=1634626734205&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-util%2Fdownload%2Fjest-util-27.3.1.tgz", "jest-validate@^26.5.2": "https://registry.npmmirror.com/jest-validate/download/jest-validate-26.6.2.tgz?cache=0&sync_timestamp=1634626742917&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-validate%2Fdownload%2Fjest-validate-26.6.2.tgz", "jest-validate@^27.3.1": "https://registry.npmmirror.com/jest-validate/download/jest-validate-27.3.1.tgz", "jest-watcher@^27.3.1": "https://registry.npmmirror.com/jest-watcher/download/jest-watcher-27.3.1.tgz?cache=0&sync_timestamp=1634626751356&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-watcher%2Fdownload%2Fjest-watcher-27.3.1.tgz", "jest-worker@^26.0.0": "https://registry.npmmirror.com/jest-worker/download/jest-worker-26.6.2.tgz", "jest-worker@^26.6.2": "https://registry.npmmirror.com/jest-worker/download/jest-worker-26.6.2.tgz", "jest-worker@^27.3.1": "https://registry.npmmirror.com/jest-worker/download/jest-worker-27.3.1.tgz?cache=0&sync_timestamp=1634626737887&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-worker%2Fdownload%2Fjest-worker-27.3.1.tgz", "jest@^27.3.1": "https://registry.npmmirror.com/jest/download/jest-27.3.1.tgz?cache=0&sync_timestamp=1634626752478&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest%2Fdownload%2Fjest-27.3.1.tgz", "jetifier@^1.6.2": "https://registry.npmmirror.com/jetifier/download/jetifier-1.6.8.tgz", "joi@^17.2.1": "https://registry.npmmirror.com/joi/download/joi-17.4.2.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "js-yaml@^3.13.1": "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmmirror.com/js-yaml/download/js-yaml-4.1.0.tgz", "jsc-android@^250230.2.1": "https://registry.npmmirror.com/jsc-android/download/jsc-android-250230.2.1.tgz", "jscodeshift@^0.11.0": "https://registry.npmmirror.com/jscodeshift/download/jscodeshift-0.11.0.tgz", "jsdom@^16.6.0": "https://registry.npmmirror.com/jsdom/download/jsdom-16.7.0.tgz?cache=0&sync_timestamp=1635866517769&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjsdom%2Fdownload%2Fjsdom-16.7.0.tgz", "jsesc@^2.5.1": "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz", "jsesc@~0.5.0": "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz", "json-parse-better-errors@^1.0.1": "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^0.5.1": "https://registry.npmmirror.com/json5/download/json5-0.5.1.tgz", "json5@^1.0.1": "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz", "json5@^2.1.2": "https://registry.npmmirror.com/json5/download/json5-2.2.0.tgz", "jsonfile@^2.1.0": "https://registry.npmmirror.com/jsonfile/download/jsonfile-2.4.0.tgz", "jsonfile@^4.0.0": "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmmirror.com/jsonfile/download/jsonfile-6.1.0.tgz", "jsonify@~0.0.0": "https://registry.npmmirror.com/jsonify/download/jsonify-0.0.0.tgz", "jsx-ast-utils@^2.4.1 || ^3.0.0": "https://registry.npmmirror.com/jsx-ast-utils/download/jsx-ast-utils-3.2.1.tgz", "kind-of@^3.0.2": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^3.0.3": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^3.2.0": "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz", "kind-of@^4.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz", "kind-of@^5.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz", "kind-of@^6.0.0": "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz", "kind-of@^6.0.2": "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz", "klaw@^1.0.0": "https://registry.npmmirror.com/klaw/download/klaw-1.3.1.tgz", "kleur@^3.0.3": "https://registry.npmmirror.com/kleur/download/kleur-3.0.3.tgz", "leven@^3.1.0": "https://registry.npmmirror.com/leven/download/leven-3.1.0.tgz", "levn@^0.4.1": "https://registry.npmmirror.com/levn/download/levn-0.4.1.tgz", "levn@~0.3.0": "https://registry.npmmirror.com/levn/download/levn-0.3.0.tgz", "locate-path@^3.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz", "locate-path@^5.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmmirror.com/locate-path/download/locate-path-6.0.0.tgz", "lodash.debounce@^4.0.8": "https://registry.npmmirror.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz", "lodash.merge@^4.6.2": "https://registry.npmmirror.com/lodash.merge/download/lodash.merge-4.6.2.tgz", "lodash.throttle@^4.1.1": "https://registry.npmmirror.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz", "lodash@^4.17.10": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz", "lodash@^4.17.14": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz", "lodash@^4.17.15": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz", "lodash@^4.7.0": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz", "log-symbols@^2.2.0": "https://registry.npmmirror.com/log-symbols/download/log-symbols-2.2.0.tgz", "logkitty@^0.7.1": "https://registry.npmmirror.com/logkitty/download/logkitty-0.7.1.tgz", "loose-envify@^1.0.0": "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz?cache=0&sync_timestamp=1632451810665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floose-envify%2Fdownload%2Floose-envify-1.4.0.tgz", "loose-envify@^1.1.0": "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz?cache=0&sync_timestamp=1632451810665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floose-envify%2Fdownload%2Floose-envify-1.4.0.tgz", "loose-envify@^1.4.0": "https://registry.npmmirror.com/loose-envify/download/loose-envify-1.4.0.tgz?cache=0&sync_timestamp=1632451810665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floose-envify%2Fdownload%2Floose-envify-1.4.0.tgz", "lru-cache@^6.0.0": "https://registry.npmmirror.com/lru-cache/download/lru-cache-6.0.0.tgz", "make-dir@^2.0.0": "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz", "make-dir@^2.1.0": "https://registry.npmmirror.com/make-dir/download/make-dir-2.1.0.tgz", "make-dir@^3.0.0": "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz", "makeerror@1.0.12": "https://registry.npmmirror.com/makeerror/download/makeerror-1.0.12.tgz?cache=0&sync_timestamp=1635238306211&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmakeerror%2Fdownload%2Fmakeerror-1.0.12.tgz", "map-cache@^0.2.2": "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz", "map-visit@^1.0.0": "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz", "merge-options@^3.0.4": "https://registry.npmmirror.com/merge-options/download/merge-options-3.0.4.tgz", "merge-stream@^2.0.0": "https://registry.npmmirror.com/merge-stream/download/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmmirror.com/merge2/download/merge2-1.4.1.tgz", "metro-babel-register@0.66.2": "https://registry.npmmirror.com/metro-babel-register/download/metro-babel-register-0.66.2.tgz", "metro-babel-transformer@0.66.2": "https://registry.npmmirror.com/metro-babel-transformer/download/metro-babel-transformer-0.66.2.tgz", "metro-cache-key@0.66.2": "https://registry.npmmirror.com/metro-cache-key/download/metro-cache-key-0.66.2.tgz", "metro-cache@0.66.2": "https://registry.npmmirror.com/metro-cache/download/metro-cache-0.66.2.tgz", "metro-config@0.66.2": "https://registry.npmmirror.com/metro-config/download/metro-config-0.66.2.tgz", "metro-config@^0.66.1": "https://registry.npmmirror.com/metro-config/download/metro-config-0.66.2.tgz", "metro-core@0.66.2": "https://registry.npmmirror.com/metro-core/download/metro-core-0.66.2.tgz", "metro-core@^0.66.1": "https://registry.npmmirror.com/metro-core/download/metro-core-0.66.2.tgz", "metro-hermes-compiler@0.66.2": "https://registry.npmmirror.com/metro-hermes-compiler/download/metro-hermes-compiler-0.66.2.tgz", "metro-inspector-proxy@0.66.2": "https://registry.npmmirror.com/metro-inspector-proxy/download/metro-inspector-proxy-0.66.2.tgz", "metro-minify-uglify@0.66.2": "https://registry.npmmirror.com/metro-minify-uglify/download/metro-minify-uglify-0.66.2.tgz", "metro-react-native-babel-preset@0.66.2": "https://registry.npmmirror.com/metro-react-native-babel-preset/download/metro-react-native-babel-preset-0.66.2.tgz", "metro-react-native-babel-preset@^0.66.2": "https://registry.npmmirror.com/metro-react-native-babel-preset/download/metro-react-native-babel-preset-0.66.2.tgz", "metro-react-native-babel-transformer@0.66.2": "https://registry.npmmirror.com/metro-react-native-babel-transformer/download/metro-react-native-babel-transformer-0.66.2.tgz", "metro-react-native-babel-transformer@^0.66.1": "https://registry.npmmirror.com/metro-react-native-babel-transformer/download/metro-react-native-babel-transformer-0.66.2.tgz", "metro-resolver@0.66.2": "https://registry.npmmirror.com/metro-resolver/download/metro-resolver-0.66.2.tgz", "metro-resolver@^0.66.1": "https://registry.npmmirror.com/metro-resolver/download/metro-resolver-0.66.2.tgz", "metro-runtime@0.66.2": "https://registry.npmmirror.com/metro-runtime/download/metro-runtime-0.66.2.tgz", "metro-runtime@^0.66.1": "https://registry.npmmirror.com/metro-runtime/download/metro-runtime-0.66.2.tgz", "metro-source-map@0.66.2": "https://registry.npmmirror.com/metro-source-map/download/metro-source-map-0.66.2.tgz", "metro-symbolicate@0.66.2": "https://registry.npmmirror.com/metro-symbolicate/download/metro-symbolicate-0.66.2.tgz", "metro-transform-plugins@0.66.2": "https://registry.npmmirror.com/metro-transform-plugins/download/metro-transform-plugins-0.66.2.tgz", "metro-transform-worker@0.66.2": "https://registry.npmmirror.com/metro-transform-worker/download/metro-transform-worker-0.66.2.tgz", "metro@0.66.2": "https://registry.npmmirror.com/metro/download/metro-0.66.2.tgz", "metro@^0.66.1": "https://registry.npmmirror.com/metro/download/metro-0.66.2.tgz", "micromatch@^3.1.10": "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz", "micromatch@^3.1.4": "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.10.tgz", "micromatch@^4.0.2": "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.4.tgz", "micromatch@^4.0.4": "https://registry.npmmirror.com/micromatch/download/micromatch-4.0.4.tgz", "mime-db@1.50.0": "https://registry.npmmirror.com/mime-db/download/mime-db-1.50.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmmirror.com/mime-db/download/mime-db-1.50.0.tgz", "mime-types@^2.1.12": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108315670&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz", "mime-types@^2.1.27": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108315670&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz", "mime-types@~2.1.24": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108315670&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz", "mime@1.6.0": "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz?cache=0&sync_timestamp=1635900684619&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime%2Fdownload%2Fmime-1.6.0.tgz", "mime@^2.4.1": "https://registry.npmmirror.com/mime/download/mime-2.6.0.tgz?cache=0&sync_timestamp=1635900684619&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime%2Fdownload%2Fmime-2.6.0.tgz", "mimic-fn@^1.0.0": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "minimatch@^3.0.2": "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz", "minimatch@^3.0.4": "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz", "minimist@^1.1.1": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "minimist@^1.2.0": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "minimist@^1.2.5": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "mixin-deep@^1.2.0": "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz", "mkdirp@^0.5.1": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz", "mkdirp@^0.5.3": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz", "moment@^2.29.1": "https://registry.npmmirror.com/moment/download/moment-2.29.1.tgz", "ms@2.0.0": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "ms@2.1.1": "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz", "ms@2.1.2": "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz", "ms@^2.1.3": "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz", "nanomatch@^1.2.9": "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz", "natural-compare@^1.4.0": "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz", "negotiator@0.6.2": "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz", "neo-async@^2.5.0": "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.2.tgz", "nice-try@^1.0.4": "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz", "nocache@^2.1.0": "https://registry.npmmirror.com/nocache/download/nocache-2.1.0.tgz", "node-dir@^0.1.17": "https://registry.npmmirror.com/node-dir/download/node-dir-0.1.17.tgz", "node-fetch@2.6.1": "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.1.tgz", "node-fetch@^2.2.0": "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.6.tgz?cache=0&sync_timestamp=1635730444620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-2.6.6.tgz", "node-fetch@^2.6.0": "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.6.tgz?cache=0&sync_timestamp=1635730444620&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-2.6.6.tgz", "node-int64@^0.4.0": "https://registry.npmmirror.com/node-int64/download/node-int64-0.4.0.tgz", "node-modules-regexp@^1.0.0": "https://registry.npmmirror.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz", "node-releases@^2.0.6": "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.6.tgz", "node-stream-zip@^1.9.1": "https://registry.npmmirror.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz", "normalize-css-color@^1.0.2": "https://registry.npmmirror.com/normalize-css-color/download/normalize-css-color-1.0.2.tgz", "normalize-path@^2.1.1": "https://registry.npmmirror.com/normalize-path/download/normalize-path-2.1.1.tgz", "normalize-path@^3.0.0": "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz", "npm-run-path@^2.0.0": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz", "npm-run-path@^4.0.1": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz", "nullthrows@^1.1.1": "https://registry.npmmirror.com/nullthrows/download/nullthrows-1.1.1.tgz", "nwsapi@^2.2.0": "https://registry.npmmirror.com/nwsapi/download/nwsapi-2.2.0.tgz", "ob1@0.66.2": "https://registry.npmmirror.com/ob1/download/ob1-0.66.2.tgz", "object-assign@^4.1.0": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz", "object-copy@^0.1.0": "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz", "object-inspect@^1.11.0": "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.0.tgz", "object-inspect@^1.9.0": "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.0.tgz", "object-keys@^1.0.12": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "object-keys@^1.1.1": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "object-visit@^1.0.0": "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz", "object.assign@^4.1.2": "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz", "object.entries@^1.1.4": "https://registry.npmmirror.com/object.entries/download/object.entries-1.1.5.tgz?cache=0&sync_timestamp=1633282037745&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.entries%2Fdownload%2Fobject.entries-1.1.5.tgz", "object.fromentries@^2.0.4": "https://registry.npmmirror.com/object.fromentries/download/object.fromentries-2.0.5.tgz?cache=0&sync_timestamp=1633282036610&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject.fromentries%2Fdownload%2Fobject.fromentries-2.0.5.tgz", "object.hasown@^1.0.0": "https://registry.npmmirror.com/object.hasown/download/object.hasown-1.1.0.tgz", "object.pick@^1.3.0": "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz", "object.values@^1.1.4": "https://registry.npmmirror.com/object.values/download/object.values-1.1.5.tgz", "on-finished@~2.3.0": "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz", "on-headers@~1.0.2": "https://registry.npmmirror.com/on-headers/download/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "once@^1.3.1": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "once@^1.4.0": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "onetime@^2.0.0": "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz", "onetime@^5.1.2": "https://registry.npmmirror.com/onetime/download/onetime-5.1.2.tgz", "open@^6.2.0": "https://registry.npmmirror.com/open/download/open-6.4.0.tgz", "optionator@^0.8.1": "https://registry.npmmirror.com/optionator/download/optionator-0.8.3.tgz", "optionator@^0.9.1": "https://registry.npmmirror.com/optionator/download/optionator-0.9.1.tgz", "options@>=0.0.5": "https://registry.npmmirror.com/options/download/options-0.0.6.tgz", "ora@^3.4.0": "https://registry.npmmirror.com/ora/download/ora-3.4.0.tgz", "os-tmpdir@^1.0.0": "https://registry.npmmirror.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz", "p-finally@^1.0.0": "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz", "p-limit@^2.0.0": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-limit@^2.2.0": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmmirror.com/p-limit/download/p-limit-3.1.0.tgz", "p-locate@^3.0.0": "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz", "p-locate@^4.1.0": "https://registry.npmmirror.com/p-locate/download/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmmirror.com/p-locate/download/p-locate-5.0.0.tgz", "p-try@^2.0.0": "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz", "parent-module@^1.0.0": "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz", "parse-json@^4.0.0": "https://registry.npmmirror.com/parse-json/download/parse-json-4.0.0.tgz?cache=0&sync_timestamp=1636011996917&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-4.0.0.tgz", "parse5@6.0.1": "https://registry.npmmirror.com/parse5/download/parse5-6.0.1.tgz", "parseurl@~1.3.3": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "pascalcase@^0.1.1": "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz", "path-exists@^3.0.0": "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz", "path-exists@^4.0.0": "https://registry.npmmirror.com/path-exists/download/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "path-key@^2.0.0": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz", "path-key@^2.0.1": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz", "path-key@^3.0.0": "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz", "path-parse@^1.0.6": "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.7.tgz?cache=0&sync_timestamp=1632469101267&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpath-parse%2Fdownload%2Fpath-parse-1.0.7.tgz", "path-to-regexp@^1.8.0": "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz", "path-type@^4.0.0": "https://registry.npmmirror.com/path-type/download/path-type-4.0.0.tgz", "picocolors@^1.0.0": "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093437726&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz", "picomatch@^2.0.4": "https://registry.npmmirror.com/picomatch/download/picomatch-2.3.0.tgz", "picomatch@^2.2.3": "https://registry.npmmirror.com/picomatch/download/picomatch-2.3.0.tgz", "pify@^4.0.1": "https://registry.npmmirror.com/pify/download/pify-4.0.1.tgz", "pirates@^4.0.0": "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz", "pirates@^4.0.1": "https://registry.npmmirror.com/pirates/download/pirates-4.0.1.tgz", "pkg-dir@^3.0.0": "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-3.0.0.tgz?cache=0&sync_timestamp=1633498184785&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-3.0.0.tgz", "pkg-dir@^4.2.0": "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1633498537676&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz", "pkg-up@^3.1.0": "https://registry.npmmirror.com/pkg-up/download/pkg-up-3.1.0.tgz", "plist@^3.0.2": "https://registry.npmmirror.com/plist/download/plist-3.0.4.tgz", "plist@^3.0.4": "https://registry.npmmirror.com/plist/download/plist-3.0.4.tgz", "posix-character-classes@^0.1.0": "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "prelude-ls@^1.2.1": "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.2.1.tgz", "prelude-ls@~1.1.2": "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.1.2.tgz", "prettier-linter-helpers@^1.0.0": "https://registry.npmmirror.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz", "prettier@^2.0.2": "https://registry.npmmirror.com/prettier/download/prettier-2.4.1.tgz", "pretty-format@^26.5.2": "https://registry.npmmirror.com/pretty-format/download/pretty-format-26.6.2.tgz?cache=0&sync_timestamp=1634626739813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-format%2Fdownload%2Fpretty-format-26.6.2.tgz", "pretty-format@^26.6.2": "https://registry.npmmirror.com/pretty-format/download/pretty-format-26.6.2.tgz?cache=0&sync_timestamp=1634626739813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-format%2Fdownload%2Fpretty-format-26.6.2.tgz", "pretty-format@^27.3.1": "https://registry.npmmirror.com/pretty-format/download/pretty-format-27.3.1.tgz?cache=0&sync_timestamp=1634626739813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpretty-format%2Fdownload%2Fpretty-format-27.3.1.tgz", "process-nextick-args@~2.0.0": "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "progress@^2.0.0": "https://registry.npmmirror.com/progress/download/progress-2.0.3.tgz", "promise@^7.1.1": "https://registry.npmmirror.com/promise/download/promise-7.3.1.tgz", "promise@^8.0.3": "https://registry.npmmirror.com/promise/download/promise-8.1.0.tgz", "prompts@^2.0.1": "https://registry.npmmirror.com/prompts/download/prompts-2.4.2.tgz", "prompts@^2.4.0": "https://registry.npmmirror.com/prompts/download/prompts-2.4.2.tgz", "prop-types@^15.6.2": "https://registry.npmmirror.com/prop-types/download/prop-types-15.7.2.tgz", "prop-types@^15.7.2": "https://registry.npmmirror.com/prop-types/download/prop-types-15.7.2.tgz", "psl@^1.1.33": "https://registry.npmmirror.com/psl/download/psl-1.8.0.tgz", "pump@^3.0.0": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "punycode@^2.1.0": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "punycode@^2.1.1": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "query-string@^6.13.6": "https://registry.npmmirror.com/query-string/download/query-string-6.14.1.tgz", "queue-microtask@^1.2.2": "https://registry.npmmirror.com/queue-microtask/download/queue-microtask-1.2.3.tgz", "range-parser@~1.2.1": "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz", "react-devtools-core@^4.13.0": "https://registry.npmmirror.com/react-devtools-core/download/react-devtools-core-4.21.0.tgz", "react-is@^16.12.0 || ^17.0.0": "https://registry.npmmirror.com/react-is/download/react-is-17.0.2.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-17.0.2.tgz", "react-is@^16.13.0": "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-16.13.1.tgz", "react-is@^16.7.0": "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-16.13.1.tgz", "react-is@^16.8.1": "https://registry.npmmirror.com/react-is/download/react-is-16.13.1.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-16.13.1.tgz", "react-is@^17.0.1": "https://registry.npmmirror.com/react-is/download/react-is-17.0.2.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-17.0.2.tgz", "react-is@^17.0.2": "https://registry.npmmirror.com/react-is/download/react-is-17.0.2.tgz?cache=0&sync_timestamp=1636128974679&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-is%2Fdownload%2Freact-is-17.0.2.tgz", "react-native-camera@^4.2.1": "https://registry.npmmirror.com/react-native-camera/-/react-native-camera-4.2.1.tgz#caf74081f055e89d7e9b0cd5108965d808c60e90", "react-native-codegen@^0.0.7": "https://registry.npmmirror.com/react-native-codegen/download/react-native-codegen-0.0.7.tgz", "react-native-collapsible@^1.6.0": "https://registry.npmmirror.com/react-native-collapsible/download/react-native-collapsible-1.6.0.tgz", "react-native-gesture-handler@^1.10.3": "https://registry.npmmirror.com/react-native-gesture-handler/download/react-native-gesture-handler-1.10.3.tgz", "react-native-iphone-x-helper@^1.3.0": "https://registry.npmmirror.com/react-native-iphone-x-helper/download/react-native-iphone-x-helper-1.3.1.tgz", "react-native-modal-popover@^2.0.1": "https://registry.npmmirror.com/react-native-modal-popover/download/react-native-modal-popover-2.1.0.tgz", "react-native-nfc-manager@^3.11.0": "https://registry.npmmirror.com/react-native-nfc-manager/download/react-native-nfc-manager-3.11.0.tgz", "react-native-pager-view@^5.4.8": "https://registry.npmmirror.com/react-native-pager-view/download/react-native-pager-view-5.4.8.tgz", "react-native-safe-area-context@^3.3.2": "https://registry.npmmirror.com/react-native-safe-area-context/download/react-native-safe-area-context-3.3.2.tgz", "react-native-safe-area-view@^0.14.9": "https://registry.npmmirror.com/react-native-safe-area-view/download/react-native-safe-area-view-0.14.9.tgz", "react-native-webview@^11.14.2": "https://registry.npmmirror.com/react-native-webview/download/react-native-webview-11.14.2.tgz", "react-native@0.66.2": "https://registry.npmmirror.com/react-native/download/react-native-0.66.2.tgz", "react-navigation-stack@^2.10.4": "https://registry.npmmirror.com/react-navigation-stack/download/react-navigation-stack-2.10.4.tgz", "react-navigation@^4.4.4": "https://registry.npmmirror.com/react-navigation/download/react-navigation-4.4.4.tgz", "react-refresh@^0.4.0": "https://registry.npmmirror.com/react-refresh/download/react-refresh-0.4.3.tgz?cache=0&sync_timestamp=1636129959244&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-refresh%2Fdownload%2Freact-refresh-0.4.3.tgz", "react-shallow-renderer@^16.13.1": "https://registry.npmmirror.com/react-shallow-renderer/download/react-shallow-renderer-16.14.1.tgz?cache=0&sync_timestamp=1632814684562&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freact-shallow-renderer%2Fdownload%2Freact-shallow-renderer-16.14.1.tgz", "react-test-renderer@17.0.2": "https://registry.npmmirror.com/react-test-renderer/download/react-test-renderer-17.0.2.tgz", "react@17.0.2": "https://registry.npmmirror.com/react/download/react-17.0.2.tgz", "readable-stream@~2.3.6": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz?cache=0&sync_timestamp=1632380409088&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Freadable-stream%2Fdownload%2Freadable-stream-2.3.7.tgz", "readline@^1.3.0": "https://registry.npmmirror.com/readline/download/readline-1.3.0.tgz", "recast@^0.20.3": "https://registry.npmmirror.com/recast/download/recast-0.20.5.tgz?cache=0&sync_timestamp=1632753969639&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Frecast%2Fdownload%2Frecast-0.20.5.tgz", "regenerate-unicode-properties@^10.1.0": "https://registry.npmmirror.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "regenerate@^1.4.2": "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.2.tgz", "regenerator-runtime@^0.11.0": "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz", "regenerator-runtime@^0.13.2": "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz", "regenerator-runtime@^0.13.4": "https://registry.npmmirror.com/regenerator-runtime/download/regenerator-runtime-0.13.9.tgz", "regenerator-transform@^0.15.0": "https://registry.npmmirror.com/regenerator-transform/-/regenerator-transform-0.15.1.tgz", "regex-not@^1.0.0": "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz", "regex-not@^1.0.2": "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz", "regexp.prototype.flags@^1.3.1": "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz", "regexpp@^3.1.0": "https://registry.npmmirror.com/regexpp/download/regexpp-3.2.0.tgz", "regexpp@^3.2.0": "https://registry.npmmirror.com/regexpp/download/regexpp-3.2.0.tgz", "regexpu-core@^5.1.0": "https://registry.npmmirror.com/regexpu-core/-/regexpu-core-5.2.2.tgz", "regjsgen@^0.7.1": "https://registry.npmmirror.com/regjsgen/-/regjsgen-0.7.1.tgz", "regjsparser@^0.9.1": "https://registry.npmmirror.com/regjsparser/-/regjsparser-0.9.1.tgz", "remove-trailing-separator@^1.0.1": "https://registry.npmmirror.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "repeat-element@^1.1.2": "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.4.tgz", "repeat-string@^1.6.1": "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz", "require-directory@^2.1.1": "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz", "require-main-filename@^2.0.0": "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-2.0.0.tgz", "reselect@^4.0.0": "https://registry.npmmirror.com/reselect/download/reselect-4.1.2.tgz", "resolve-cwd@^3.0.0": "https://registry.npmmirror.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz", "resolve-from@^3.0.0": "https://registry.npmmirror.com/resolve-from/download/resolve-from-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmmirror.com/resolve-from/download/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmmirror.com/resolve-from/download/resolve-from-5.0.0.tgz", "resolve-url@^0.2.1": "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz", "resolve.exports@^1.1.0": "https://registry.npmmirror.com/resolve.exports/download/resolve.exports-1.1.0.tgz", "resolve@^1.12.0": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "resolve@^1.13.1": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "resolve@^1.14.2": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "resolve@^1.20.0": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "resolve@^1.3.2": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "resolve@^2.0.0-next.3": "https://registry.npmmirror.com/resolve/download/resolve-2.0.0-next.3.tgz", "restore-cursor@^2.0.0": "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz", "ret@~0.1.10": "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz", "reusify@^1.0.4": "https://registry.npmmirror.com/reusify/download/reusify-1.0.4.tgz", "rimraf@^2.5.4": "https://registry.npmmirror.com/rimraf/download/rimraf-2.7.1.tgz", "rimraf@^3.0.0": "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz", "rimraf@^3.0.2": "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz", "rimraf@~2.2.6": "https://registry.npmmirror.com/rimraf/download/rimraf-2.2.8.tgz", "rsvp@^4.8.4": "https://registry.npmmirror.com/rsvp/download/rsvp-4.8.5.tgz", "run-parallel@^1.1.9": "https://registry.npmmirror.com/run-parallel/download/run-parallel-1.2.0.tgz", "safe-buffer@5.1.2": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.0": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "safe-regex@^1.1.0": "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "sane@^4.0.3": "https://registry.npmmirror.com/sane/download/sane-4.1.0.tgz", "sax@>=0.6.0": "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz", "sax@^1.2.1": "https://registry.npmmirror.com/sax/download/sax-1.2.4.tgz", "saxes@^5.0.1": "https://registry.npmmirror.com/saxes/download/saxes-5.0.1.tgz", "scheduler@^0.20.2": "https://registry.npmmirror.com/scheduler/download/scheduler-0.20.2.tgz", "semver@^5.3.0": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^5.5.0": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^5.6.0": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz", "semver@^6.0.0": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^6.1.1": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^6.1.2": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^6.3.0": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz", "semver@^7.2.1": "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1632728822118&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "semver@^7.3.2": "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1632728822118&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "semver@^7.3.5": "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1632728822118&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "send@0.17.1": "https://registry.npmmirror.com/send/download/send-0.17.1.tgz", "serialize-error@^2.1.0": "https://registry.npmmirror.com/serialize-error/download/serialize-error-2.1.0.tgz", "serve-static@^1.13.1": "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.1.tgz", "set-blocking@^2.0.0": "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz", "set-value@^2.0.0": "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz", "set-value@^2.0.1": "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz", "setimmediate@^1.0.5": "https://registry.npmmirror.com/setimmediate/download/setimmediate-1.0.5.tgz", "setprototypeof@1.1.1": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz", "shallow-clone@^3.0.0": "https://registry.npmmirror.com/shallow-clone/download/shallow-clone-3.0.1.tgz", "shallowequal@^1.1.0": "https://registry.npmmirror.com/shallowequal/download/shallowequal-1.1.0.tgz", "shebang-command@^1.2.0": "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmmirror.com/shebang-command/download/shebang-command-2.0.0.tgz", "shebang-regex@^1.0.0": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "shell-quote@1.6.1": "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.6.1.tgz?cache=0&sync_timestamp=1634798333958&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fshell-quote%2Fdownload%2Fshell-quote-1.6.1.tgz", "shell-quote@^1.6.1": "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.6.1.tgz?cache=0&sync_timestamp=1634798333958&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fshell-quote%2Fdownload%2Fshell-quote-1.6.1.tgz", "side-channel@^1.0.4": "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.4.tgz", "signal-exit@^3.0.0": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632949289206&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "signal-exit@^3.0.2": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632949289206&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "signal-exit@^3.0.3": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632949289206&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "simple-plist@^1.0.0": "https://registry.npmmirror.com/simple-plist/download/simple-plist-1.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsimple-plist%2Fdownload%2Fsimple-plist-1.3.0.tgz", "simple-plist@^1.1.0": "https://registry.npmmirror.com/simple-plist/download/simple-plist-1.3.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsimple-plist%2Fdownload%2Fsimple-plist-1.3.0.tgz", "simple-swizzle@^0.2.2": "https://registry.npmmirror.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "sisteransi@^1.0.5": "https://registry.npmmirror.com/sisteransi/download/sisteransi-1.0.5.tgz", "slash@^3.0.0": "https://registry.npmmirror.com/slash/download/slash-3.0.0.tgz", "slice-ansi@^2.0.0": "https://registry.npmmirror.com/slice-ansi/download/slice-ansi-2.1.0.tgz?cache=0&sync_timestamp=1632753426896&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fslice-ansi%2Fdownload%2Fslice-ansi-2.1.0.tgz", "snapdragon-node@^2.0.1": "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "snapdragon-util@^3.0.1": "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "snapdragon@^0.8.1": "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz", "source-map-resolve@^0.5.0": "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz", "source-map-support@^0.5.16": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.20.tgz", "source-map-support@^0.5.6": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.20.tgz", "source-map-url@^0.4.0": "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.1.tgz", "source-map@^0.5.0": "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz", "source-map@^0.5.6": "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmmirror.com/source-map/download/source-map-0.7.3.tgz", "source-map@~0.6.1": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "split-on-first@^1.0.0": "https://registry.npmmirror.com/split-on-first/download/split-on-first-1.1.0.tgz", "split-string@^3.0.1": "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz", "split-string@^3.0.2": "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz", "sprintf-js@~1.0.2": "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "stack-utils@^2.0.3": "https://registry.npmmirror.com/stack-utils/download/stack-utils-2.0.5.tgz", "stackframe@^1.1.1": "https://registry.npmmirror.com/stackframe/download/stackframe-1.2.0.tgz", "stacktrace-parser@^0.1.3": "https://registry.npmmirror.com/stacktrace-parser/download/stacktrace-parser-0.1.10.tgz", "static-extend@^0.1.1": "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz", "statuses@>= 1.5.0 < 2": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz", "statuses@~1.5.0": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz", "stream-buffers@2.2.x": "https://registry.npmmirror.com/stream-buffers/download/stream-buffers-2.2.0.tgz", "strict-uri-encode@^2.0.0": "https://registry.npmmirror.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz", "string-length@^4.0.1": "https://registry.npmmirror.com/string-length/download/string-length-4.0.2.tgz", "string-width@^4.1.0": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz", "string.prototype.matchall@^4.0.5": "https://registry.npmmirror.com/string.prototype.matchall/download/string.prototype.matchall-4.0.6.tgz", "string.prototype.trimend@^1.0.4": "https://registry.npmmirror.com/string.prototype.trimend/download/string.prototype.trimend-1.0.4.tgz", "string.prototype.trimstart@^1.0.4": "https://registry.npmmirror.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.4.tgz", "string_decoder@~1.1.1": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz", "strip-ansi@^5.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "strip-ansi@^5.2.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-5.2.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "strip-bom@^4.0.0": "https://registry.npmmirror.com/strip-bom/download/strip-bom-4.0.0.tgz", "strip-eof@^1.0.0": "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz", "strip-final-newline@^2.0.0": "https://registry.npmmirror.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz", "strip-json-comments@^3.1.0": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "strip-json-comments@^3.1.1": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "sudo-prompt@^9.0.0": "https://registry.npmmirror.com/sudo-prompt/download/sudo-prompt-9.2.1.tgz", "supports-color@^5.3.0": "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz", "supports-color@^7.0.0": "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmmirror.com/supports-color/download/supports-color-8.1.1.tgz?cache=0&sync_timestamp=1632381094751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-8.1.1.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmmirror.com/supports-hyperlinks/download/supports-hyperlinks-2.2.0.tgz", "symbol-tree@^3.2.4": "https://registry.npmmirror.com/symbol-tree/download/symbol-tree-3.2.4.tgz?cache=0&sync_timestamp=1632753453543&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsymbol-tree%2Fdownload%2Fsymbol-tree-3.2.4.tgz", "temp@0.8.3": "https://registry.npmmirror.com/temp/download/temp-0.8.3.tgz", "temp@^0.8.1": "https://registry.npmmirror.com/temp/download/temp-0.8.3.tgz", "terminal-link@^2.0.0": "https://registry.npmmirror.com/terminal-link/download/terminal-link-2.1.1.tgz", "test-exclude@^6.0.0": "https://registry.npmmirror.com/test-exclude/download/test-exclude-6.0.0.tgz", "text-table@^0.2.0": "https://registry.npmmirror.com/text-table/download/text-table-0.2.0.tgz", "throat@^5.0.0": "https://registry.npmmirror.com/throat/download/throat-5.0.0.tgz", "throat@^6.0.1": "https://registry.npmmirror.com/throat/download/throat-6.0.1.tgz", "through2@^2.0.1": "https://registry.npmmirror.com/through2/download/through2-2.0.5.tgz", "tmpl@1.0.5": "https://registry.npmmirror.com/tmpl/download/tmpl-1.0.5.tgz", "to-fast-properties@^2.0.0": "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "to-object-path@^0.3.0": "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz", "to-regex-range@^2.1.0": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz", "to-regex-range@^5.0.1": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "to-regex@^3.0.1": "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz", "to-regex@^3.0.2": "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz", "toidentifier@1.0.0": "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz", "tough-cookie@^4.0.0": "https://registry.npmmirror.com/tough-cookie/download/tough-cookie-4.0.0.tgz?cache=0&sync_timestamp=1632456062832&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftough-cookie%2Fdownload%2Ftough-cookie-4.0.0.tgz", "tr46@^2.1.0": "https://registry.npmmirror.com/tr46/download/tr46-2.1.0.tgz", "tr46@~0.0.3": "https://registry.npmmirror.com/tr46/download/tr46-0.0.3.tgz", "tslib@^1.13.0": "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz", "tslib@^1.8.1": "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz", "tslib@^2.0.1": "https://registry.npmmirror.com/tslib/download/tslib-2.3.1.tgz", "tslint@^6.1.3": "https://registry.npmmirror.com/tslint/download/tslint-6.1.3.tgz", "tsutils@^2.29.0": "https://registry.npmmirror.com/tsutils/download/tsutils-2.29.0.tgz?cache=0&sync_timestamp=1632753977026&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftsutils%2Fdownload%2Ftsutils-2.29.0.tgz", "tsutils@^3.21.0": "https://registry.npmmirror.com/tsutils/download/tsutils-3.21.0.tgz?cache=0&sync_timestamp=1632753977026&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftsutils%2Fdownload%2Ftsutils-3.21.0.tgz", "type-check@^0.4.0": "https://registry.npmmirror.com/type-check/download/type-check-0.4.0.tgz", "type-check@~0.3.2": "https://registry.npmmirror.com/type-check/download/type-check-0.3.2.tgz", "type-check@~0.4.0": "https://registry.npmmirror.com/type-check/download/type-check-0.4.0.tgz", "type-detect@4.0.8": "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz", "type-fest@^0.20.2": "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1635390799031&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmmirror.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1635390799031&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz", "type-fest@^0.7.1": "https://registry.npmmirror.com/type-fest/download/type-fest-0.7.1.tgz", "typedarray-to-buffer@^3.1.5": "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "ua-parser-js@^0.7.30": "https://registry.npmmirror.com/ua-parser-js/download/ua-parser-js-0.7.31.tgz", "uglify-es@^3.1.9": "https://registry.npmmirror.com/uglify-es/download/uglify-es-3.3.9.tgz", "ultron@1.0.x": "https://registry.npmmirror.com/ultron/download/ultron-1.0.2.tgz", "unbox-primitive@^1.0.1": "https://registry.npmmirror.com/unbox-primitive/download/unbox-primitive-1.0.1.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.1.0": "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.0.0.tgz", "union-value@^1.0.0": "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz", "universalify@^0.1.0": "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz", "universalify@^0.1.2": "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz", "universalify@^1.0.0": "https://registry.npmmirror.com/universalify/download/universalify-1.0.0.tgz", "universalify@^2.0.0": "https://registry.npmmirror.com/universalify/download/universalify-2.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz", "unset-value@^1.0.0": "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz", "update-browserslist-db@^1.0.9": "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "uri-js@^4.2.2": "https://registry.npmmirror.com/uri-js/download/uri-js-4.4.1.tgz", "urix@^0.1.0": "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz", "use-subscription@^1.0.0": "https://registry.npmmirror.com/use-subscription/download/use-subscription-1.5.1.tgz", "use@^3.1.0": "https://registry.npmmirror.com/use/download/use-3.1.1.tgz", "util-deprecate@~1.0.1": "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "utility-types@^3.10.0": "https://registry.npmmirror.com/utility-types/download/utility-types-3.10.0.tgz", "utils-merge@1.0.1": "https://registry.npmmirror.com/utils-merge/download/utils-merge-1.0.1.tgz", "uuid@^3.3.2": "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz", "uuid@^7.0.3": "https://registry.npmmirror.com/uuid/download/uuid-7.0.3.tgz?cache=0&sync_timestamp=1632753721412&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fuuid%2Fdownload%2Fuuid-7.0.3.tgz", "v8-compile-cache@^2.0.3": "https://registry.npmmirror.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz", "v8-to-istanbul@^8.1.0": "https://registry.npmmirror.com/v8-to-istanbul/download/v8-to-istanbul-8.1.0.tgz", "vary@~1.1.2": "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz", "vlq@^1.0.0": "https://registry.npmmirror.com/vlq/download/vlq-1.0.1.tgz", "w3c-hr-time@^1.0.2": "https://registry.npmmirror.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz", "w3c-xmlserializer@^2.0.0": "https://registry.npmmirror.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz?cache=0&sync_timestamp=1632753506598&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fw3c-xmlserializer%2Fdownload%2Fw3c-xmlserializer-2.0.0.tgz", "walker@^1.0.7": "https://registry.npmmirror.com/walker/download/walker-1.0.8.tgz?cache=0&sync_timestamp=1635238260872&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwalker%2Fdownload%2Fwalker-1.0.8.tgz", "walker@~1.0.5": "https://registry.npmmirror.com/walker/download/walker-1.0.8.tgz?cache=0&sync_timestamp=1635238260872&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwalker%2Fdownload%2Fwalker-1.0.8.tgz", "wcwidth@^1.0.1": "https://registry.npmmirror.com/wcwidth/download/wcwidth-1.0.1.tgz", "webidl-conversions@^3.0.0": "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz", "webidl-conversions@^5.0.0": "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz", "webidl-conversions@^6.1.0": "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz", "whatwg-encoding@^1.0.5": "https://registry.npmmirror.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz", "whatwg-fetch@^3.0.0": "https://registry.npmmirror.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz", "whatwg-mimetype@^2.3.0": "https://registry.npmmirror.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz", "whatwg-url@^5.0.0": "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-5.0.0.tgz?cache=0&sync_timestamp=1634673664859&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-5.0.0.tgz", "whatwg-url@^8.0.0": "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-8.7.0.tgz", "whatwg-url@^8.5.0": "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-8.7.0.tgz", "which-boxed-primitive@^1.0.2": "https://registry.npmmirror.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz", "which-module@^2.0.0": "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz", "which@^1.2.9": "https://registry.npmmirror.com/which/download/which-1.3.1.tgz", "which@^2.0.1": "https://registry.npmmirror.com/which/download/which-2.0.2.tgz", "word-wrap@^1.2.3": "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz", "word-wrap@~1.2.3": "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz", "wrap-ansi@^6.2.0": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "wrappy@1": "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz", "write-file-atomic@^2.3.0": "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-2.4.3.tgz", "write-file-atomic@^3.0.0": "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz?cache=0&sync_timestamp=1632755000530&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrite-file-atomic%2Fdownload%2Fwrite-file-atomic-3.0.3.tgz", "ws@^1.1.0": "https://registry.npmmirror.com/ws/download/ws-1.1.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-1.1.5.tgz", "ws@^1.1.5": "https://registry.npmmirror.com/ws/download/ws-1.1.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-1.1.5.tgz", "ws@^6.1.4": "https://registry.npmmirror.com/ws/download/ws-6.2.2.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-6.2.2.tgz", "ws@^7": "https://registry.npmmirror.com/ws/download/ws-7.5.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-7.5.5.tgz", "ws@^7.4.6": "https://registry.npmmirror.com/ws/download/ws-7.5.5.tgz?cache=0&sync_timestamp=1633200039582&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fws%2Fdownload%2Fws-7.5.5.tgz", "xcode@^2.0.0": "https://registry.npmmirror.com/xcode/download/xcode-2.1.0.tgz", "xcode@^3.0.1": "https://registry.npmmirror.com/xcode/download/xcode-3.0.1.tgz", "xml-name-validator@^3.0.0": "https://registry.npmmirror.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz?cache=0&sync_timestamp=1632753518490&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fxml-name-validator%2Fdownload%2Fxml-name-validator-3.0.0.tgz", "xml2js@^0.4.23": "https://registry.npmmirror.com/xml2js/download/xml2js-0.4.23.tgz", "xmlbuilder@^14.0.0": "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-14.0.0.tgz", "xmlbuilder@^9.0.7": "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-9.0.7.tgz", "xmlbuilder@~11.0.0": "https://registry.npmmirror.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz", "xmlchars@^2.2.0": "https://registry.npmmirror.com/xmlchars/download/xmlchars-2.2.0.tgz?cache=0&sync_timestamp=1632753519311&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fxmlchars%2Fdownload%2Fxmlchars-2.2.0.tgz", "xmldoc@^1.1.2": "https://registry.npmmirror.com/xmldoc/download/xmldoc-1.1.2.tgz", "xtend@~4.0.1": "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz", "y18n@^4.0.0": "https://registry.npmmirror.com/y18n/download/y18n-4.0.3.tgz", "y18n@^5.0.5": "https://registry.npmmirror.com/y18n/download/y18n-5.0.8.tgz", "yallist@^4.0.0": "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz", "yargs-parser@^18.1.2": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-18.1.3.tgz?cache=0&sync_timestamp=1632728414140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-18.1.3.tgz", "yargs-parser@^20.2.2": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1632728414140&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz", "yargs@^15.1.0": "https://registry.npmmirror.com/yargs/download/yargs-15.4.1.tgz", "yargs@^15.3.1": "https://registry.npmmirror.com/yargs/download/yargs-15.4.1.tgz", "yargs@^16.2.0": "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz", "yocto-queue@^0.1.0": "https://registry.npmmirror.com/yocto-queue/download/yocto-queue-0.1.0.tgz"}, "files": [], "artifacts": {}}