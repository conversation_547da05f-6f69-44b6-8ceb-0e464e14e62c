[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\tooltip_frame_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\tooltip_frame_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_text_cursor_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_text_cursor_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_tab_indicator_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_tab_indicator_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_item_background_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_item_background_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_list_selector_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_list_selector_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\tooltip_frame_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\tooltip_frame_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_item_background_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_item_background_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_list_selector_background_transition_holo_light.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_list_selector_background_transition_holo_light.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_textfield_search_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_textfield_search_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_ab_back_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_ab_back_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\notification_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2780a7d58323a095a84203f45c645c6e\\transformed\\core-1.1.0\\res\\drawable\\notification_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\redbox_top_border_background.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a21ceb2dee016b3bc178279e85108ab3\\transformed\\jetified-react-native-0.66.2\\res\\drawable\\redbox_top_border_background.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_list_selector_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_list_selector_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_voice_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_voice_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\notification_icon_background.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2780a7d58323a095a84203f45c645c6e\\transformed\\core-1.1.0\\res\\drawable\\notification_icon_background.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_seekbar_thumb_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_seekbar_thumb_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\notification_tile_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2780a7d58323a095a84203f45c645c6e\\transformed\\core-1.1.0\\res\\drawable\\notification_tile_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_menu_overflow_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_menu_overflow_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_seekbar_tick_mark_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_seekbar_tick_mark_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\notification_bg_low.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2780a7d58323a095a84203f45c645c6e\\transformed\\core-1.1.0\\res\\drawable\\notification_bg_low.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_spinner_textfield_background_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_spinner_textfield_background_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ratingbar_indicator_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ratingbar_indicator_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_btn_borderless_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_btn_borderless_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_vector_test.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_vector_test.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_seekbar_track_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_seekbar_track_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_go_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_go_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_cab_background_internal_bg.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_cab_background_internal_bg.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_list_selector_background_transition_holo_dark.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_btn_default_mtrl_shape.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_btn_default_mtrl_shape.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_cab_background_top_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_cab_background_top_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_btn_radio_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_btn_radio_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_switch_thumb_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_switch_thumb_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_btn_check_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_btn_check_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ratingbar_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ratingbar_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_search_api_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_search_api_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ratingbar_small_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ratingbar_small_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\drawable\\abc_ic_clear_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\drawable\\abc_ic_clear_material.xml"}]