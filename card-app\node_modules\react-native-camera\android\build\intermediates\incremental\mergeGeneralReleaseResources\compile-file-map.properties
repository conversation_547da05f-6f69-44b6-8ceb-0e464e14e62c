#Mon Aug 11 23:01:38 CST 2025
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_ab_back_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_ab_back_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_seek_thumb.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_seek_thumb.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v21\\abc_btn_colored_borderless_text_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v21\\abc_btn_colored_borderless_text_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_cut_mtrl_alpha.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_cut_mtrl_alpha.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout-watch-v20\\abc_alert_dialog_button_bar_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-watch-v20\\abc_alert_dialog_button_bar_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_big_media_narrow.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_big_media_narrow.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_background_transition_holo_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_list_selector_background_transition_holo_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_text_select_handle_right_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_divider_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_media_custom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_media_custom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_divider_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_divider_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_textfield_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_indicator_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ratingbar_indicator_material.xml
D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\src\\main\\res\\layout\\surface_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\surface_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_edittext.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_edittext.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light_normal.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_light_normal.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\xml\\rn_dev_preferences.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\xml\\rn_dev_preferences.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_search_dropdown_item_icons_2line.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_search_dropdown_item_icons_2line.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_btn_checkable.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_btn_checkable.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_icon_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\notification_icon_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v21\\abc_action_bar_item_background_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\abc_action_bar_item_background_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_right_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_text_select_handle_right_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_checkbox_checked_to_unchecked_mtrl_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_btn_colored_borderless_text_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_btn_colored_borderless_text_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_popup_background_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_popup_background_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_background_cache_hint_selector_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_background_cache_hint_selector_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\notification_template_part_chronometer.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_part_chronometer.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_mode_close_item_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_mode_close_item_material.xml
D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\src\\main\\res\\layout-v14\\texture_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v14\\texture_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_default.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_default.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark_focused.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_dark_focused.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_primary_text_disable_only_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_primary_text_disable_only_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\tooltip_frame_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\tooltip_frame_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_low_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\notification_bg_low_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_secondary_text_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_secondary_text_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-v21\\notification_action_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\notification_action_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_text_select_handle_left_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_text_select_handle_left_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_text_select_handle_left_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\googleg_disabled_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\googleg_disabled_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_btn_colored_text_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_btn_colored_text_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\custom_dialog.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\custom_dialog.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v21\\abc_btn_colored_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\abc_btn_colored_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_media_cancel_action.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_media_cancel_action.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_close_exit.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_close_exit.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_check_material_anim.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_check_material_anim.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_check_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_check_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_seek_thumb.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_seek_thumb.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-ldrtl-xhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-ldrtl-xhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_tooltip_enter.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_tooltip_enter.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_clear_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_clear_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_holo_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_list_selector_holo_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_expanded_menu_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_expanded_menu_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\notification_bg_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\be9076cd4fc3ff6ad98aa6fb277b3148\\transformed\\jetified-autofill-1.1.0\\res\\layout\\autofill_inline_suggestion.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\autofill_inline_suggestion.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_out_bottom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_out_bottom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_open_exit.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_open_exit.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_in_bottom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_in_bottom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_template_icon_group.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_template_icon_group.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_text_select_handle_middle_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_multichoice_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\select_dialog_multichoice_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_media_action.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_media_action.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_search_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_search_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_activity_chooser_view_list_item.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_activity_chooser_view_list_item.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_shrink_fade_out_from_bottom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_shrink_fade_out_from_bottom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_popup_background_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_popup_background_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v23\\abc_control_background_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v23\\abc_control_background_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_fade_out.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_fade_out.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_text_select_handle_right_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light_focused.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_light_focused.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_title_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_alert_dialog_title_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_text_select_handle_left_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_star_half_black_48dp.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_star_half_black_48dp.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_popup_exit.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_popup_exit.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_primary_text_disable_only_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_primary_text_disable_only_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\googleg_standard_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\googleg_standard_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_1.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v21\\abc_list_divider_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\abc_list_divider_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_pressed_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_pressed_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\fast_out_slow_in.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\fast_out_slow_in.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_content_include.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_screen_content_include.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_bar_up_container.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_bar_up_container.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_icon.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_list_menu_item_icon.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v21\\abc_dialog_material_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\abc_dialog_material_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_item_title.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\redbox_item_title.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-ldrtl-xxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-ldrtl-xxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_switch_track.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_switch_track.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_out_top.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_out_top.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_tile_bg.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\notification_tile_bg.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_holo_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_list_selector_holo_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-hdpi-v4\\notification_bg_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\notification_bg_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\redbox_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_singlechoice_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\select_dialog_singlechoice_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_fade_enter.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_fade_enter.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\switch_thumb_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\switch_thumb_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_star_black_48dp.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_star_black_48dp.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_primary_text_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_primary_text_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_focused_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_focused_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_background_cache_hint_selector_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_background_cache_hint_selector_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_cab_background_internal_bg.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_cab_background_internal_bg.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_item_background_holo_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_item_background_holo_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_disabled.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_disabled.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_longpressed_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_longpressed_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_copy_mtrl_am_alpha.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_copy_mtrl_am_alpha.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_popup_background_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_radio.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_list_menu_item_radio.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_text_select_handle_middle_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_text_select_handle_middle_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_switch_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_radio_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_radio_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\menu\\example_menu.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\menu\\example_menu.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_1.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ratingbar_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_pressed_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_cascading_menu_item_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_cascading_menu_item_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_action_tombstone.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_action_tombstone.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-ldrtl-xxxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-ldrtl-xxxhdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_tooltip.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_tooltip.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\support_simple_spinner_dropdown_item.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\support_simple_spinner_dropdown_item.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_item_background_holo_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_item_background_holo_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_text_select_handle_right_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_text_select_handle_right_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_pressed_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_pressed_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark_normal.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_dark_normal.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_pressed_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_pressed_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\common_full_open_on_phone.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\common_full_open_on_phone.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_switch_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_selector_disabled_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_pressed_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_longpressed_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_longpressed_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_edittext.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_edittext.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_toolbar.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_screen_toolbar.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_default_mtrl_shape.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_default_mtrl_shape.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_on_mtrl.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_radio_on_mtrl.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_slide_down.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_slide_down.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_fade_out.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_fade_out.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_radio_to_off_mtrl_animation_interpolator_0.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_spinner.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_spinner.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_text_select_handle_left_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_text_select_handle_left_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_popup_background_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_popup_background_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_hint_foreground_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_hint_foreground_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_list_menu_item_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_spinner_textfield_background_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_spinner_textfield_background_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_btn_colored_text_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_btn_colored_text_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\anim-v21\\fragment_fast_out_extra_slow_in.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim-v21\\fragment_fast_out_extra_slow_in.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_scrubber_control_off_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_radio_to_on_mtrl_animation_interpolator_0.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_longpressed_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout\\notification_template_part_time.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_part_time.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_fade_in.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_fade_in.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_divider_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_divider_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_middle_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_text_select_handle_middle_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_off_to_on_mtrl_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_radio_off_to_on_mtrl_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_push_up_out.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_push_up_out.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_divider_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_divider_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\switch_thumb_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\switch_thumb_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_switch_thumb_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_switch_thumb_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_template_custom_big.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_template_custom_big.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_mode_bar.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_mode_bar.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_borderless_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_borderless_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_focused_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_focused_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_unchecked_mtrl.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_checkbox_unchecked_mtrl.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_on_to_off_mtrl_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_radio_on_to_off_mtrl_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-v21\\abc_edit_text_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v21\\abc_edit_text_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_checkbox_unchecked_to_checked_mtrl_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout-watch-v20\\abc_alert_dialog_title_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-watch-v20\\abc_alert_dialog_title_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_switch_track.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_switch_track.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_select_dialog_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_select_dialog_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_fade_in.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_fade_in.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_search_api_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_search_api_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_arrow_drop_right_black_24dp.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_checkbox_checked_mtrl.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_checkbox_checked_mtrl.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\color\\common_google_signin_btn_text_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\common_google_signin_btn_text_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_tab_indicator_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_tab_indicator_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_checkbox_unchecked_mtrl_animation_interpolator_0.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_radio_to_on_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\interpolator\\btn_checkbox_checked_mtrl_animation_interpolator_0.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_radio_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_menu_item_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_menu_item_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_go_search_api_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_go_search_api_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_check_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\tooltip_frame_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\tooltip_frame_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_scrubber_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_simple_overlay_action_mode.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_screen_simple_overlay_action_mode.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_btn_check_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_switch_to_on_mtrl_00001.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\color\\common_google_signin_btn_tint.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\common_google_signin_btn_tint.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\test_level_drawable.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\test_level_drawable.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_text_select_handle_right_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_text_select_handle_right_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_tint_default.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_tint_default.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_btn_radio_material_anim.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_btn_radio_material_anim.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_spinner.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_spinner.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_bar_title_item.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_bar_title_item.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_alert_dialog_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_selector_disabled_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_lines_media.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_lines_media.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_textfield_search_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_textfield_search_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\drawable\\redbox_top_border_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\redbox_top_border_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_push_up_in.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_push_up_in.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\redbox_item_frame.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\redbox_item_frame.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\dev_loading_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\dev_loading_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_popup_menu_item_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_popup_menu_item_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_paste_mtrl_am_alpha.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_paste_mtrl_am_alpha.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_open_enter.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_open_enter.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_media.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_media.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_switch_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_secondary_text_material_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_secondary_text_material_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_hint_foreground_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_hint_foreground_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_text_select_handle_left_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_text_select_handle_left_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_list_menu_item_checkbox.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_list_menu_item_checkbox.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark_normal.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_dark_normal.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_pressed_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_pressed_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_share_mtrl_alpha.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_share_mtrl_alpha.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_text_cursor_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_text_cursor_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_dialog_title_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_dialog_title_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_alert_dialog_button_bar_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_alert_dialog_button_bar_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_bg.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\notification_bg.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\common_google_signin_btn_text_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_textfield_search_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\layout\\fps_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\fps_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_screen_simple.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_screen_simple.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ratingbar_small_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ratingbar_small_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\common_full_open_on_phone.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\common_full_open_on_phone.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_cab_background_top_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_cab_background_top_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_text_select_handle_middle_mtrl.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_text_select_handle_middle_mtrl.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_list_focused_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_list_focused_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_color_highlight_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_color_highlight_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_search_url_text.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_search_url_text.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\be9076cd4fc3ff6ad98aa6fb277b3148\\transformed\\jetified-autofill-1.1.0\\res\\drawable-v29\\autofill_inline_suggestion_chip_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-v29\\autofill_inline_suggestion_chip_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\common_google_signin_btn_icon_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_tick_mark_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_seekbar_tick_mark_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_big_media.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_big_media.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_longpressed_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_longpressed_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_textfield_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_selectall_mtrl_alpha.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_selectall_mtrl_alpha.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable\\notification_bg_low.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\notification_bg_low.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_grow_fade_in_from_bottom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_grow_fade_in_from_bottom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_dark_focused.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_dark_focused.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_scrubber_primary_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_tooltip_exit.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_tooltip_exit.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light_focused.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_light_focused.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_action_menu_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_action_menu_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\color\\common_google_signin_btn_text_light.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\common_google_signin_btn_text_light.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_big_media_narrow_custom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_big_media_narrow_custom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_ic_commit_search_api_mtrl_alpha.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_slide_up.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_slide_up.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_voice_search_api_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_voice_search_api_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-hdpi-v4\\notification_bg_low_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\notification_bg_low_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_textfield_activated_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_close_enter.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_close_enter.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color\\abc_primary_text_material_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color\\abc_primary_text_material_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_cab_background_top_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-ldrtl-mdpi-v17\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-ldrtl-mdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_popup_enter.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_popup_enter.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_textfield_search_default_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_track_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_seekbar_track_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_icon_disabled.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_icon_disabled.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-watch-v20\\abc_dialog_material_background.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-watch-v20\\abc_dialog_material_background.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\googleg_standard_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\googleg_standard_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_icon_null_animation.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_icon_null_animation.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_ab_share_pack_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\googleg_disabled_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\googleg_disabled_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_in_top.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_in_top.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_seekbar_thumb_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_seekbar_thumb_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_selector_disabled_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_list_focused_holo.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_list_focused_holo.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\color-v23\\abc_tint_btn_checkable.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\color-v23\\abc_tint_btn_checkable.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-xhdpi-v4\\notification_bg_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\notification_bg_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable\\common_google_signin_btn_text_light_normal.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\common_google_signin_btn_text_light_normal.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_activity_chooser_view.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_activity_chooser_view.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout-v26\\abc_screen_toolbar.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v26\\abc_screen_toolbar.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\layout-v21\\notification_action.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout-v21\\notification_action.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\googleg_standard_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\googleg_standard_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\googleg_disabled_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\googleg_disabled_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\btn_radio_off_mtrl.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\btn_radio_off_mtrl.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_list_selector_background_transition_holo_dark.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_list_selector_background_transition_holo_dark.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\abc_popup_menu_header_item_layout.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\abc_popup_menu_header_item_layout.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_tab_indicator_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_switch_track_mtrl_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_switch_track_mtrl_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\6002c4bad2084e99b3580f0d613eae70\\transformed\\fragment-1.3.6\\res\\animator\\fragment_fade_exit.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\animator\\fragment_fade_exit.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-ldrtl-hdpi-v17\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-ldrtl-hdpi-v17\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_list_pressed_holo_dark.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_list_pressed_holo_dark.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\googleg_standard_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\googleg_standard_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_scrubber_control_to_pressed_mtrl_005.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\abc_spinner_mtrl_am_alpha.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_list_pressed_holo_light.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_list_pressed_holo_light.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\menu\\example_menu2.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\menu\\example_menu2.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-hdpi-v4\\googleg_disabled_color_18.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\googleg_disabled_color_18.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\945f679d5066ec5cea4c418de5c0d6c7\\transformed\\media-1.0.0\\res\\layout\\notification_template_big_media_custom.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\notification_template_big_media_custom.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\abc_btn_switch_to_on_mtrl_00012.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_btn_check_to_on_mtrl_015.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\layout\\select_dialog_item_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\layout\\select_dialog_item_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxxhdpi-v4\\abc_scrubber_control_to_pressed_mtrl_000.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\07e69b1ced29bd04061c2b8491a4f094\\transformed\\jetified-appcompat-resources-1.4.1\\res\\drawable\\abc_vector_test.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_vector_test.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-xxhdpi-v4\\common_google_signin_btn_icon_dark_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\drawable-mdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-mdpi-v4\\common_google_signin_btn_text_light_normal_background.9.png
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable\\abc_ic_menu_overflow_material.xml=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable\\abc_ic_menu_overflow_material.xml
C\:\\Users\\insub\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\drawable-hdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png=D\:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\drawable-hdpi-v4\\abc_menu_hardkey_panel_mtrl_mult.9.png
