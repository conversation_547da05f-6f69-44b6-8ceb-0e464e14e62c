1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.reactnative.camera" >
4
5    <uses-sdk
6        android:minSdkVersion="21"
6-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml
7        android:targetSdkVersion="30" />
7-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml
8
9    <uses-permission android:name="android.permission.CAMERA" />
9-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:2:3-63
9-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:2:20-60
10
11    <uses-feature
11-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:4:3-83
12        android:name="android.hardware.camera"
12-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:4:17-55
13        android:required="false" />
13-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:4:56-80
14    <uses-feature
14-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:5:3-93
15        android:name="android.hardware.camera.autofocus"
15-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:5:17-65
16        android:required="false" />
16-->D:\Sites\card-all\card-app\node_modules\react-native-camera\android\src\main\AndroidManifest.xml:5:66-90
17
18</manifest>
