{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-community\\masked-view\\android\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-watch-v21\\values-watch-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}, {"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\@react-native-community\\masked-view\\android\\build\\intermediates\\res\\merged\\release\\values-watch-v21\\values-watch-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-watch-v21\\values-watch-v21.xml", "from": {"startLines": "2,6,10", "startColumns": "4,4,4", "startOffsets": "55,271,499", "endLines": "5,9,13", "endColumns": "12,12,12", "endOffsets": "266,494,724"}}]}]}