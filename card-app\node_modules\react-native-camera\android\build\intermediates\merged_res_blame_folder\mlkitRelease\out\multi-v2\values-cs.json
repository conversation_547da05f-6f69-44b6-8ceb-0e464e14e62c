{"logs": [{"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\mlkit\\release\\values-cs\\values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5623", "endColumns": "100", "endOffsets": "5719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "201", "endOffsets": "448"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3951", "endColumns": "201", "endOffsets": "4148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,541,664,772,984,1109,1247,1373,1607,1716,1911,2037,2257,2438,2532,2625", "endColumns": "103,191,122,107,211,124,137,125,233,108,194,125,219,180,93,92,110", "endOffsets": "348,540,663,771,983,1108,1246,1372,1606,1715,1910,2036,2256,2437,2531,2624,2735"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2799,2907,3099,3226,3338,3550,3679,3821,4153,4387,4500,4695,4825,5045,5230,5328,5425", "endColumns": "107,191,126,111,211,128,141,129,233,112,194,129,219,184,97,96,114", "endOffsets": "2902,3094,3221,3333,3545,3674,3816,3946,4382,4495,4690,4820,5040,5225,5323,5420,5535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,5540", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,5618"}}]}, {"outputFile": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\incremental\\mergeMlkitReleaseResources\\merged.dir\\values-cs\\values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9a5609492583665f69031e1d92193180\\transformed\\core-1.7.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5623", "endColumns": "100", "endOffsets": "5719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\060dff7644d80534b68ae20379930fc6\\transformed\\jetified-play-services-basement-17.6.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "201", "endOffsets": "448"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3951", "endColumns": "201", "endOffsets": "4148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dae511b76fc5abe0b43b7dfd722fa90e\\transformed\\jetified-play-services-base-17.6.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "245,349,541,664,772,984,1109,1247,1373,1607,1716,1911,2037,2257,2438,2532,2625", "endColumns": "103,191,122,107,211,124,137,125,233,108,194,125,219,180,93,92,110", "endOffsets": "348,540,663,771,983,1108,1246,1372,1606,1715,1910,2036,2256,2437,2531,2624,2735"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2799,2907,3099,3226,3338,3550,3679,3821,4153,4387,4500,4695,4825,5045,5230,5328,5425", "endColumns": "107,191,126,111,211,128,141,129,233,112,194,129,219,184,97,96,114", "endOffsets": "2902,3094,3221,3333,3545,3674,3816,3946,4382,4495,4690,4820,5040,5225,5323,5420,5535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,5540", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,5618"}}]}]}