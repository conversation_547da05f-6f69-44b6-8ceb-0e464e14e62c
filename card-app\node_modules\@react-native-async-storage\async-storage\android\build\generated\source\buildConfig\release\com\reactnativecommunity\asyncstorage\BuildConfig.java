/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.reactnativecommunity.asyncstorage;

public final class BuildConfig {
  public static final boolean DEBUG = false;
  public static final String LIBRARY_PACKAGE_NAME = "com.reactnativecommunity.asyncstorage";
  public static final String BUILD_TYPE = "release";
  // Field from default config.
  public static final Long AsyncStorage_db_size = 6L;
  // Field from default config.
  public static final boolean AsyncStorage_useDedicatedExecutor = false;
  // Field from default config.
  public static final boolean AsyncStorage_useNextStorage = false;
}
