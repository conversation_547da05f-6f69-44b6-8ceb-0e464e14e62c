[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_switch_track.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_switch_track.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_color_highlight_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_color_highlight_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_edittext.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_edittext.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_seek_thumb.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_seek_thumb.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_btn_colored_borderless_text_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_btn_colored_borderless_text_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_btn_checkable.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_btn_checkable.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_default.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_default.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_btn_colored_text_material.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_btn_colored_text_material.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-safe-area-context\\android\\build\\intermediates\\res\\merged\\release\\color-v23\\abc_tint_spinner.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bb9561c589f5cd8f762c133b8ca94b9\\transformed\\appcompat-1.0.2\\res\\color-v23\\abc_tint_spinner.xml"}]