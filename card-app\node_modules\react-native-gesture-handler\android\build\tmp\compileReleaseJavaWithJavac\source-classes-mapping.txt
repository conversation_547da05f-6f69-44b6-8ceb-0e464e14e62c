com/swmansion/gesturehandler/react/RNGestureHandlerPackage.java
 com.swmansion.gesturehandler.react.RNGestureHandlerPackage
com/swmansion/gesturehandler/NativeViewGestureHandler.java
 com.swmansion.gesturehandler.NativeViewGestureHandler
com/swmansion/gesturehandler/react/RNGestureHandlerStateChangeEvent.java
 com.swmansion.gesturehandler.react.RNGestureHandlerStateChangeEvent
com/swmansion/gesturehandler/PointerEventsConfig.java
 com.swmansion.gesturehandler.PointerEventsConfig
com/swmansion/gesturehandler/ViewConfigurationHelper.java
 com.swmansion.gesturehandler.ViewConfigurationHelper
com/swmansion/gesturehandler/TapGestureHandler.java
 com.swmansion.gesturehandler.TapGestureHandler
 com.swmansion.gesturehandler.TapGestureHandler$1
com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.java
 com.swmansion.gesturehandler.react.RNGestureHandlerRootViewManager
com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.java
 com.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManager
 com.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManager$ButtonViewGroup
 com.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManager$ButtonViewGroup$1
com/swmansion/gesturehandler/GestureHandlerRegistryImpl.java
 com.swmansion.gesturehandler.GestureHandlerRegistryImpl
com/swmansion/gesturehandler/GestureHandlerOrchestrator.java
 com.swmansion.gesturehandler.GestureHandlerOrchestrator
 com.swmansion.gesturehandler.GestureHandlerOrchestrator$1
com/swmansion/gesturehandler/react/RNGestureHandlerRootView.java
 com.swmansion.gesturehandler.react.RNGestureHandlerRootView
com/swmansion/gesturehandler/RotationGestureHandler.java
 com.swmansion.gesturehandler.RotationGestureHandler
 com.swmansion.gesturehandler.RotationGestureHandler$1
com/swmansion/gesturehandler/BaseGestureHandlerInteractionController.java
 com.swmansion.gesturehandler.BaseGestureHandlerInteractionController
com/swmansion/gesturehandler/react/RNGestureHandlerModule.java
 com.swmansion.gesturehandler.react.RNGestureHandlerModule
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$1
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$2
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$FlingGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$HandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$LongPressGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$NativeViewGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$PanGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$PinchGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$RotationGestureHandlerFactory
 com.swmansion.gesturehandler.react.RNGestureHandlerModule$TapGestureHandlerFactory
com/swmansion/gesturehandler/GestureHandlerInteractionController.java
 com.swmansion.gesturehandler.GestureHandlerInteractionController
com/swmansion/gesturehandler/react/BuildConfig.java
 com.swmansion.gesturehandler.react.BuildConfig
com/swmansion/gesturehandler/react/RNGestureHandlerEnabledRootView.java
 com.swmansion.gesturehandler.react.RNGestureHandlerEnabledRootView
com/swmansion/gesturehandler/OnTouchEventListener.java
 com.swmansion.gesturehandler.OnTouchEventListener
com/facebook/react/views/modal/RNGHModalUtils.java
 com.facebook.react.views.modal.RNGHModalUtils
com/swmansion/gesturehandler/GestureUtils.java
 com.swmansion.gesturehandler.GestureUtils
com/swmansion/gesturehandler/GestureHandlerRegistry.java
 com.swmansion.gesturehandler.GestureHandlerRegistry
com/swmansion/gesturehandler/PanGestureHandler.java
 com.swmansion.gesturehandler.PanGestureHandler
com/swmansion/gesturehandler/react/RNGestureHandlerRootHelper.java
 com.swmansion.gesturehandler.react.RNGestureHandlerRootHelper
 com.swmansion.gesturehandler.react.RNGestureHandlerRootHelper$1
 com.swmansion.gesturehandler.react.RNGestureHandlerRootHelper$RootViewGestureHandler
com/swmansion/gesturehandler/LongPressGestureHandler.java
 com.swmansion.gesturehandler.LongPressGestureHandler
 com.swmansion.gesturehandler.LongPressGestureHandler$1
com/swmansion/gesturehandler/PinchGestureHandler.java
 com.swmansion.gesturehandler.PinchGestureHandler
 com.swmansion.gesturehandler.PinchGestureHandler$1
com/swmansion/gesturehandler/react/RNGestureHandlerRegistry.java
 com.swmansion.gesturehandler.react.RNGestureHandlerRegistry
 com.swmansion.gesturehandler.react.RNGestureHandlerRegistry$1
com/swmansion/gesturehandler/react/RNGestureHandlerInteractionManager.java
 com.swmansion.gesturehandler.react.RNGestureHandlerInteractionManager
com/swmansion/gesturehandler/RotationGestureDetector.java
 com.swmansion.gesturehandler.RotationGestureDetector
 com.swmansion.gesturehandler.RotationGestureDetector$OnRotationGestureListener
com/swmansion/gesturehandler/FlingGestureHandler.java
 com.swmansion.gesturehandler.FlingGestureHandler
 com.swmansion.gesturehandler.FlingGestureHandler$1
com/swmansion/gesturehandler/react/RNGestureHandlerEventDataExtractor.java
 com.swmansion.gesturehandler.react.RNGestureHandlerEventDataExtractor
com/swmansion/gesturehandler/react/RNViewConfigurationHelper.java
 com.swmansion.gesturehandler.react.RNViewConfigurationHelper
 com.swmansion.gesturehandler.react.RNViewConfigurationHelper$1
com/swmansion/gesturehandler/GestureHandler.java
 com.swmansion.gesturehandler.GestureHandler
 com.swmansion.gesturehandler.GestureHandler$1
com/swmansion/gesturehandler/react/RNGestureHandlerRootInterface.java
 com.swmansion.gesturehandler.react.RNGestureHandlerRootInterface
com/swmansion/gesturehandler/react/RNGestureHandlerEvent.java
 com.swmansion.gesturehandler.react.RNGestureHandlerEvent
