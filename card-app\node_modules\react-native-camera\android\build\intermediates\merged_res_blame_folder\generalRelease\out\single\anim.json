[{"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_box_inner_merged_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_in_top.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_in_top.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_icon_null_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_icon_null_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_tooltip_exit.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_tooltip_exit.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_path_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_slide_down.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_slide_down.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_push_up_in.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_push_up_in.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_fade_in.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_fade_in.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_ring_outer_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_slide_up.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_slide_up.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_icon_null_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_out_top.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_out_top.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_off_mtrl_dot_group_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_fade_out.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_fade_out.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_in_bottom.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_in_bottom.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_ring_outer_path_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_tooltip_enter.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_tooltip_enter.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_slide_out_bottom.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_slide_out_bottom.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_popup_exit.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_popup_exit.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_grow_fade_in_from_bottom.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_grow_fade_in_from_bottom.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_radio_to_on_mtrl_dot_group_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_box_inner_merged_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_shrink_fade_out_from_bottom.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_shrink_fade_out_from_bottom.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_unchecked_check_path_merged_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_fade_in.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_fade_in.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_fade_out.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_fade_out.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\abc_popup_enter.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\abc_popup_enter.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d3d5b4e7c41854b612980f161edd44cd\\transformed\\appcompat-1.4.1\\res\\anim\\btn_checkbox_to_checked_box_outer_merged_animation.xml"}, {"merged": "D:\\Sites\\card-all\\card-app\\node_modules\\react-native-camera\\android\\build\\intermediates\\res\\merged\\general\\release\\anim\\catalyst_push_up_out.xml", "source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7499bb59eb268577a31358ac87695103\\transformed\\jetified-react-native-0.71.0-rc.0-release\\res\\anim\\catalyst_push_up_out.xml"}]